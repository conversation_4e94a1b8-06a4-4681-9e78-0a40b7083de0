package logger

import (
	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
)

var ServiceLogger *logrus.Logger
var APILogger *logrus.Logger
var DBQueryLogger *logrus.Logger

func ConfigLogger(logDir, logLevel string, maxSize int, maxAge int) {
	configureServiceLogger(logDir, logLevel, maxSize, maxAge)
}

func configureServiceLogger(logDir, logLevel string, maxSize int, maxAge int) {
	ServiceLogger = logrus.New()
	APILogger = logrus.New()
	DBQueryLogger = logrus.New()
	level, _ := logrus.ParseLevel(logLevel)
	ServiceLogger.SetLevel(level)
	APILogger.SetLevel(level)
	DBQueryLogger.SetLevel(level)
	timeFormatter := new(logrus.TextFormatter)
	timeFormatter.TimestampFormat = "dd-MMMM-yyyy hh:mm:ss a"
	logrus.SetFormatter(timeFormatter)
	fsLogger := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "service.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	ServiceLogger.SetOutput(fsLogger)
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP)
	go func() {
		for {
			<-c
			err := fsLogger.Rotate()
			if err != nil {
				return
			}
		}
	}()
	fsLogger2 := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "api.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	APILogger.SetOutput(fsLogger2)
	c4 := make(chan os.Signal, 1)
	signal.Notify(c4, syscall.SIGHUP)
	go func() {
		for {
			<-c4
			err := fsLogger2.Rotate()
			if err != nil {
				return
			}
		}
	}()
	fsLogger3 := &lumberjack.Logger{
		Filename:  filepath.Join(logDir, "db_query.log"),
		MaxSize:   maxSize, // MB
		MaxAge:    maxAge,  // Days
		Compress:  true,
		LocalTime: true,
	}
	DBQueryLogger.SetOutput(fsLogger3)
	c5 := make(chan os.Signal, 1)
	signal.Notify(c5, syscall.SIGHUP)
	go func() {
		for {
			<-c5
			err := fsLogger3.Rotate()
			if err != nil {
				return
			}
		}
	}()
}
