package ubuntu

import "patch-central-repo/model"

type UbuntuReleasePackage struct {
	model.BaseEntityModel
	NoticeID       string `bun:"type:varchar(50)"`
	OSVersion      string `bun:"type:varchar(50)"`
	Description    string `bun:"type:varchar(500)"`
	IsSource       bool   `json:"is_source"`
	IsVisible      bool
	Pocket         string `bun:"type:varchar(50)"`
	SourceLink     string `bun:"type:varchar(500)" json:"source_link"`
	Version        string `bun:"type:varchar(100)" json:"version"`
	VersionLink    string `bun:"type:varchar(500)" json:"version_link"`
	NameAndVersion string `bun:"type:varchar(250)"`
}
