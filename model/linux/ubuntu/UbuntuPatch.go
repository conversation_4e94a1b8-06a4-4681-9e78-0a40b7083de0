package ubuntu

import "patch-central-repo/model"

type UbuntuPatch struct {
	model.BaseEntityModel
	UUID              string `bun:"type:varchar(250)" json:"uuid"`
	OsVersion         string `bun:"type:varchar(50)" json:"os_version"`
	Channel           string `bun:"type:varchar(50)" json:"channel"`
	Repo              string `bun:"type:varchar(1000)" json:"repo"`
	PackageName       string `bun:"type:varchar(500)" json:"Package"`
	Arch              string `bun:"type:varchar(50)" json:"Architecture"`
	Version           string `bun:"type:varchar(250)" json:"Version"`
	Priority          string `bun:"type:varchar(50)" json:"Priority"`
	Section           string `bun:"type:varchar(50)" json:"Section"`
	Origin            string `bun:"type:varchar(50)" json:"Origin"`
	Depends           string `json:"Depends"`
	Breaks            string `json:"Breaks"`
	FileName          string `json:"FileName"`
	DownloadUrl       string `json:"DownloadUrl"`
	Size              int64  `json:"Size"`
	Sha1              string `json:"Sha1"`
	PkgAndVersion     string `bun:"type:varchar(1000)" json:"PkgAndVersion"`
	PkgNameWithDistro string `bun:"type:varchar(1000)" json:"PkgNameWithDistro"`
	Downloadable      bool   `json:"Downloadable"`
	ReleaseDate       int64  `json:"ReleaseDate"`
}
