package ubuntu

import "patch-central-repo/model"

type UbuntuNoticeData struct {
	model.BaseEntityModel
	CVEsIDs         []string `json:"cves_ids"`
	NoticeId        string   `bun:"type:varchar(100)" json:"id"`
	Description     string   `json:"description"`
	Instructions    string   `bun:"type:varchar(1000)" json:"instructions"`
	IsHidden        bool     `json:"is_hidden"`
	Published       string   `bun:"type:varchar(100)" json:"published"`
	Summary         string   `bun:"type:varchar(1000)" json:"summary"`
	Title           string   `bun:"type:varchar(250)" json:"title"`
	Type            string   `bun:"type:varchar(50)" json:"type"`
	AffectedOS      string   `bun:"type:varchar(250)" json:"affected_os"`
	SupportURL      string   `bun:"type:varchar(250)" json:"support_url"`
	ReleaseDate     int64
	ReleasePackages map[string][]UbuntuReleasePackage `json:"release_packages"`
}
