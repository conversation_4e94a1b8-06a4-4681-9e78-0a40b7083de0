package ubuntu

import "patch-central-repo/model"

type LinuxPackage struct {
	model.BaseEntityModel
	Description  string `bun:"type:varchar(1000)" json:"description"`
	Section      string `bun:"type:varchar(100)" json:"section"`
	Distribution string `bun:"type:varchar(100)" json:"distribution"`
	OldVersion   string `bun:"type:varchar(100)" json:"oldVersion"`
	Arch         string `bun:"type:varchar(50)" json:"arch"`
	ReleaseDate  int64  `json:"releaseDate"`
	Version      string `bun:"type:varchar(250)" json:"version"`
}
