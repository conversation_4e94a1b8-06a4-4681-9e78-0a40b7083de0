package windows

import "patch-central-repo/model"

type PatchCategory struct {
	model.BaseEntityModel
	UUID         string `bun:"type:varchar(100)" json:"uuid"`
	CategoryType string `bun:"type:varchar(50)" json:"categoryType"`
	ParentId     int64  `json:"parentId"`
	ParentUUID   string `bun:"type:varchar(100)" json:"parentUuid"`
	Description  string `json:"description"`
	ParentName   string `json:"parentName"`
}
