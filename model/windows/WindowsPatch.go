package windows

import "patch-central-repo/model"

type WindowsPatch struct {
	model.BaseEntityModel
	TempData                                bool                     `json:"tempData"`
	ReplaceById                             int64                    `json:"replaceById"`
	ReplaceByUuid                           string                   `bun:"type:varchar(100)" json:"replaceByUuid"`
	RevisionId                              int64                    `json:"revisionId"`
	UUID                                    string                   `bun:"type:varchar(100),notnull,unique" json:"uuid"`
	Title                                   string                   `bun:"type:varchar(1000)" json:"title"`
	KbId                                    string                   `bun:"type:varchar(50)" json:"kbId"`
	Description                             string                   `bun:"type:varchar(1000)" json:"description"`
	HasReplacement                          bool                     `json:"hasReplacement"`
	Leaf                                    bool                     `json:"leaf"`
	Declined                                bool                     `json:"declined"`
	PublishState                            string                   `bun:"type:varchar(100)" json:"publishState"`
	UpdateType                              string                   `bun:"type:varchar(100)" json:"updateType"`
	BulletinId                              string                   `bun:"type:varchar(100)" json:"bulletinId"`
	InstallationCanRequestUserInput         bool                     `json:"installationCanRequestUserInput"`
	InstallationRequiresNetworkConnectivity bool                     `json:"installationRequiresNetworkConnectivity"`
	CanUninstall                            bool                     `json:"canUninstall"`
	InstallationImpact                      string                   `bun:"type:varchar(100)" json:"installationImpact"`
	RestartBehaviour                        string                   `bun:"type:varchar(100)" json:"restartBehaviour"`
	SupportedLanguage                       string                   `bun:"type:varchar(100)" json:"supportedLanguage"`
	LastUpdatedTime                         int64                    `json:"lastUpdatedTime"`
	WsusLastUpdatedTime                     int64                    `json:"wsusLastUpdatedTime"`
	FileDetails                             []model.FileData         `json:"fileDetails"`
	SupersedesString                        string                   `json:"supersedesString"`
	Company                                 string                   `bun:"type:varchar(100)" json:"company"`
	ProductFamily                           string                   `bun:"type:varchar(100)" json:"productFamily"`
	Products                                string                   `bun:"type:varchar(1000)" json:"products"`
	ProductsUuid                            []string                 `json:"productsUuid"`
	Classification                          string                   `bun:"type:varchar(100)" json:"classification"`
	CveNumber                               string                   `json:"cveNumber"`
	Severity                                string                   `bun:"type:varchar(100)" json:"severity"`
	MoreInfoUrl                             string                   `bun:"type:varchar(500)" json:"moreInfoUrl"`
	DownloadUrl                             []string                 `json:"downloadUrl"`
	ReleaseDate                             int64                    `bun:"type:varchar(100)" json:"releaseDate"`
	Arch                                    string                   `bun:"type:varchar(50)" json:"arch"`
	OsName                                  string                   `bun:"type:varchar(100)" json:"osName"`
	OsArch                                  string                   `json:"osArch"`
	ProductId                               int64                    `json:"productId"`
	MsrcPatchId                             int64                    `json:"msrcPatchId"`
	AffectedProduct                         []string                 `json:"affectedProduct"`
	Rules                                   []map[string]interface{} `json:"rules"`
	KbIdToBeInstalled                       string                   `json:"kbIdToBeInstalled"`
	CabExist                                bool                     `json:"cabExist"`
	AtLeastOneFileInstallation              bool                     `json:"atLeastOneFileInstallation"`
}
