package windows

import "patch-central-repo/model"

type PatchProduct struct {
	model.BaseEntityModel
	UUID            string `bun:"type:varchar(100)" json:"uuid"`
	ServicePack     string `bun:"type:varchar(100)" json:"servicePack"`
	OriginalText    string `bun:"type:varchar(500)" json:"originalText"`
	DisplayName     string `bun:"type:varchar(100)" json:"displayName"`
	Description     string `bun:"type:varchar(500)" json:"description"`
	SubCategory     string `bun:"type:varchar(100)" json:"subCategory"`
	SubCategoryUUID string `bun:"type:varchar(100)" json:"subCategoryUUID"`
}
