package model

type FileData struct {
	FileName       string `bun:"type:varchar(100)" json:"fileName"`
	DownloadUrl    string `bun:"type:varchar(100)" json:"downloadUrl"`
	Size           int64  `json:"size"`
	ReleaseDate    int64  `json:"releaseDate"`
	Language       int64  `json:"language"`
	ChecksumSHA256 string `bun:"type:varchar(1000)" json:"checksumSha256"`
	FetchFromMSU   bool   `json:"fetchFromMSU"`
}
