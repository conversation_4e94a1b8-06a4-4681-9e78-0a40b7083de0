package macos

import "patch-central-repo/model"

type MacOsPatch struct {
	model.BaseEntityModel
	OsVersion            string           `bun:"type:varchar(50)" json:"osVersion"`
	ProductKey           string           `bun:"type:varchar(100)" json:"productKey"`
	ReleaseDate          int64            `json:"releaseDate"`
	Description          string           `bun:"type:varchar(1000)" json:"description"`
	Version              string           `bun:"type:varchar(50)" json:"version"`
	DistributionFileName string           `bun:"type:varchar(500)" json:"distributionFileName"`
	ProductType          string           `bun:"type:varchar(50)" json:"productType"`
	Packages             []model.FileData `json:"packages"`
}
