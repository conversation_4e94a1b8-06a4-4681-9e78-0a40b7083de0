package handler

import (
	"fmt"
	"github.com/gorilla/mux"
	"io"
	"mime"
	"net/http"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/service/windows"
	"path"
	"strings"
)

func XmlDownloadFile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[XmlDownloadFile] Error while getting file with name :", err.Error())
			return
		}
		return
	}

	//Check file exist or not
	filePath := fmt.Sprintf("%s/%s", common.XMLDirectoryPath(), filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		filePath = fmt.Sprintf("%s/%s", common.ThirdPartyXMLDirectoryPath(), filename)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			logger.ServiceLogger.Error("[XmlDownloadFile] File not found", err.Error())
			http.Error(w, "File not found", http.StatusNotFound)
			return
		}

	}

	if strings.Contains(filename, ".tmp") {
		err := os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	//Get Fileinfo
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		logger.ServiceLogger.Error("[XmlDownloadFile] Error accessing file", err.Error())
		http.Error(w, "Error accessing file", http.StatusInternalServerError)
		return
	}

	// Set response headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename)))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Open file and write to response
	file, err := os.Open(filePath)
	if err != nil {
		logger.ServiceLogger.Error("[XmlDownloadFile] Error opening file", err.Error())
		http.Error(w, "Error opening file", http.StatusInternalServerError)
		return
	}
	defer func(file *os.File) {
		err = file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[XmlDownloadFile]", err.Error())
		}
	}(file)

	_, err1 := io.Copy(w, file)
	if err1 != nil {
		logger.ServiceLogger.Error("[XmlDownloadFile]", err1.Error())
		return
	}
}

func CabDownloadFile(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadFile] Error while getting file with name : ", err.Error())
			return
		}
		return
	}

	//Check file exist or not
	filePath := fmt.Sprintf("%s/%s", common.CabDataDirectoryPath(), filename+".7z")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		_, err = windows.GetCabByUpdateId(filename)
		if err != nil {
			jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusNotFound))
			w.WriteHeader(http.StatusNotFound)
			_, err = fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[CabDownloadFile] File not found ", err.Error())
				return
			}
			return
		}
	}

	if strings.Contains(filename, ".tmp") {
		err := os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		jsonData, _ := common.RestToJson(w, common.Error("Error accessing file "+filename, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadFile] Error accessing file ", err.Error())
			return
		}
		return
	}

	//Get Fileinfo
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error accessing file "+filename, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadFile] Error accessing file ", err.Error())
			return
		}
		return
	}

	// Set response headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename+".7z"))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename+".7z")))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Open file and write to response
	file, err := os.Open(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error opening file "+filename, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadFile] Error opening file ", err.Error())
			return
		}
		return
	}
	defer func(file *os.File) {
		err = file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadFile] Error closing file ", err.Error())
		}
	}(file)

	_, err1 := io.Copy(w, file)
	if err1 != nil {
		logger.ServiceLogger.Error("[CabDownloadFile] Error copying file ", err1.Error())
		return
	}
}

func CabDownloadDirectory(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	filename, _ := vars["filename"]
	if filename == "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting file with name : "+filename, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadDirectory] Error while getting file with name : ", err.Error())
			return
		}
		return
	}

	//Check file exist or not
	filePath := fmt.Sprintf("%s/%s", common.CabDataDirectoryPath(), filename+".7z")
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		if err != nil {
			jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusNotFound))
			w.WriteHeader(http.StatusNotFound)
			_, err = fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[CabDownloadDirectory] File not found ", err.Error())
				return
			}
			return
		}
	}

	if strings.Contains(filename, ".tmp") {
		err := os.Remove(filePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		jsonData, _ := common.RestToJson(w, common.Error("Error accessing file "+filename, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadDirectory] Error accessing file ", err.Error())
			return
		}
		return
	}

	//Get Fileinfo
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error accessing file "+filename, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadDirectory] Error accessing file ", err.Error())
			return
		}
		return
	}

	// Set response headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename+".7z"))
	w.Header().Set("Content-Type", mime.TypeByExtension(path.Ext(filename+".7z")))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Open file and write to response
	file, err := os.Open(filePath)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error opening file "+filename, http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadDirectory] Error opening file ", err.Error())
			return
		}
		return
	}
	defer func(file *os.File) {
		err = file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[CabDownloadDirectory] Error closing file ", err.Error())
		}
	}(file)

	_, err1 := io.Copy(w, file)
	if err1 != nil {
		logger.ServiceLogger.Error("[CabDownloadDirectory] Error copying file ", err1.Error())
		return
	}
}
