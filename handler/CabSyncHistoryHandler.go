package handler

import (
	"fmt"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/rest"
	"patch-central-repo/service/windows"
)

type CabSyncHistoryHandler struct {
	Service *windows.CabSyncHistoryService
}

func NewCabSyncHistoryHandler() *CabSyncHistoryHandler {
	return &CabSyncHistoryHandler{Service: windows.NewCabSyncHistoryService()}
}

func (handler CabSyncHistoryHandler) GetAllWindowsCabHistory(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pageResponse, err := handler.Service.GetAllWindowsCabHistory(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pageResponse)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}
