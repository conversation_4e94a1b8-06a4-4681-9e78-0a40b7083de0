package handler

import (
	"fmt"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/rest"
	"patch-central-repo/service/linux/ubuntu"
)

type UbuntuPatchHandler struct {
	UbuntuPatchService          *ubuntu.UbuntuPatchService
	LinuxPackageService         *ubuntu.LinuxPackageService
	UbuntuNoticeDataService     *ubuntu.UbuntuNoticeDataService
	UbuntuReleasePackageService *ubuntu.UbuntuReleasePackageService
}

func NewUbuntuPatchHandler() *UbuntuPatchHandler {
	return &UbuntuPatchHandler{
		UbuntuPatchService:          ubuntu.NewUbuntuPatchService(),
		LinuxPackageService:         ubuntu.NewLinuxPackageService(),
		UbuntuNoticeDataService:     ubuntu.NewUbuntuNoticeDataService(),
		UbuntuReleasePackageService: ubuntu.NewUbuntuReleasePackageService(),
	}
}

func (handler UbuntuPatchHandler) GetAllUbuntuPatch(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pageResponse, err := handler.UbuntuPatchService.GetAllUbuntuPatch(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pageResponse)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}

func (handler UbuntuPatchHandler) GetAllLinuxPackages(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pageResponse, err := handler.LinuxPackageService.GetAllLinuxPackage(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pageResponse)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}

func (handler UbuntuPatchHandler) GetAllNoticeData(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pageResponse, err := handler.UbuntuNoticeDataService.GetAllUbuntuNotice(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pageResponse)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}

func (handler UbuntuPatchHandler) GetAllReleasePackage(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pageResponse, err := handler.UbuntuReleasePackageService.GetAllUbuntuReleasePackage(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pageResponse)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}
