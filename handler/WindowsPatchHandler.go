package handler

import (
	"fmt"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/rest"
	"patch-central-repo/service/windows"
)

type WindowsPatchHandler struct {
	Service *windows.WindowsPatchService
}

func NewWindowsPatchHandler() *WindowsPatchHandler {
	return &WindowsPatchHandler{Service: windows.NewWindowsPatchService()}
}

func (handler WindowsPatchHandler) GetAllWindowsPatch(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	pageResponse, err := handler.Service.GetAllWindowsPatch(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.Write<PERSON>eader(http.StatusInternalServerError)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, pageResponse)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}

func (handler WindowsPatchHandler) GetAllCategoryXmlList(w http.ResponseWriter, r *http.Request) {
	fileNameList := handler.Service.GetAllXmlFileName()
	jsonData, _ := common.RestToJson(w, map[string]interface{}{"result": fileNameList})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}

func (handler WindowsPatchHandler) PrepareXmlZip(w http.ResponseWriter, r *http.Request) {
	handler.Service.Create7ZXmlFolder()
	jsonData, _ := common.RestToJson(w, map[string]interface{}{"result": "success"})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}
