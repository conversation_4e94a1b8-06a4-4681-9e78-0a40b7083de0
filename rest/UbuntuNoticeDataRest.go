package rest

type UbuntuNoticeDataRest struct {
	BaseEntityRest
	CVEsIDs         []string                              `json:"cvesIds"`
	NoticeId        string                                `json:"noticeId"`
	Description     string                                `json:"description"`
	Instructions    string                                `json:"instructions"`
	IsHidden        bool                                  `json:"isHidden"`
	Published       string                                `json:"published"`
	Summary         string                                `json:"summary"`
	Title           string                                `json:"title"`
	Type            string                                `json:"type"`
	AffectedOS      string                                `json:"affectedOs"`
	SupportURL      string                                `json:"supportUrl"`
	ReleaseDate     int64                                 `json:"releaseDate"`
	ReleasePackages map[string][]UbuntuReleasePackageRest `json:"releasePackages"`
}
