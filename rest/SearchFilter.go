package rest

import (
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"strings"
)

type SearchFilter struct {
	Offset          int             `json:"offset"`
	Size            int             `json:"size"`
	IncludeArchived bool            `json:"archived"`
	SortBy          string          `json:"sortBy"`
	Qualification   []Qualification `json:"qualification"`
}

type Qualification struct {
	Column    string `json:"column"`
	Operator  string `json:"operator"`
	Value     string `json:"value"`
	Condition string `json:"condition"`
}

func BuildQualification(column, operator, value, condition string) Qualification {
	return Qualification{
		Column:    column,
		Operator:  operator,
		Value:     value,
		Condition: condition,
	}
}

func PrepareQueryFromSearchFilter(filter SearchFilter, tableName string, isCountQual bool) string {
	columnSelection := "*"
	if isCountQual {
		columnSelection = "count(*)"
	}
	query := fmt.Sprintf("select %s from %s ", columnSelection, tableName)
	qualifications := filter.Qualification

	if len(qualifications) > 0 {
		query += fmt.Sprintf(" where ")
	}

	isConditionAppednded := false
	for index, qualification := range qualifications {
		if index != 0 && !isConditionAppednded {
			query += " OR "
		}

		isConditionAppednded = false
		column := qualification.Column
		column = common.ToSnakeCase(column)
		operator := qualification.Operator
		value := qualification.Value

		if "os" == strings.ToLower(column) {
			var osType common.OsType
			os, err := osType.ToOsType(strings.ToLower(value))
			if err == nil {
				query += " \"os\" = " + fmt.Sprintf("%d", os) + " "
			}
		} else if strings.EqualFold("contains", strings.ToLower(operator)) {
			query += "\"" + column + "\" ILIKE '%" + value + "%' "
		} else if strings.EqualFold("not_contains", strings.ToLower(qualification.Operator)) {
			query += "\"" + column + "\" NOT ILIKE '%" + value + "%' "
		} else if strings.EqualFold("equals", strings.ToLower(operator)) {
			query += "\"" + column + "\" = '" + value + "' "
		} else if strings.EqualFold("not_equals", strings.ToLower(qualification.Operator)) {
			query += "\"" + column + "\" != '" + value + "' "
		} else if strings.EqualFold("in", strings.ToLower(operator)) {
			query += "\"" + column + "\" IN (" + value + ") "
		} else if strings.EqualFold("not_in", strings.ToLower(operator)) {
			query += "\"" + column + "\" Not IN (" + value + ") "
		} else if strings.EqualFold(">=", strings.ToLower(operator)) {
			query += "\"" + column + "\" >= '" + value + "' "
		} else if strings.EqualFold("<=", strings.ToLower(operator)) {
			query += "\"" + column + "\" <= '" + value + "' "
		}

		if index != len(qualifications)-1 && "" != qualification.Condition {
			isConditionAppednded = true
			query += qualification.Condition + " "
		}
	}
	if !isCountQual {
		if filter.SortBy != "" {
			sortBy := strings.Split(filter.SortBy, ",")
			var sortClauses []string
			for _, sortByValue := range sortBy {
				direction := "DESC"
				if strings.Contains(sortByValue, "-") {
					direction = "ASC"
					sortByValue = strings.ReplaceAll(sortByValue, "-", "")
				}
				sortByValue = common.ToSnakeCase(sortByValue)
				sortClauses = append(sortClauses, fmt.Sprintf("\"%s\" %s", sortByValue, direction))
			}
			if len(sortClauses) > 0 {
				query += "order by " + strings.Join(sortClauses, ", ") + " "
			}
		} else {
			query += fmt.Sprintf("order by \"%s\" %s ", "id", "DESC")
		}

		if filter.Size > 0 {
			query += fmt.Sprintf("offset %v limit %v ", filter.Offset, filter.Size)
		}
	}
	query += ";"
	logger.ServiceLogger.Debug("query", query)
	return query
}

func PrepareQueryFromSearchFilterWithCondition(filter SearchFilter, tableName string, isCountQual bool, queryCondition string) string {
	columnSelection := "*"
	if isCountQual {
		columnSelection = "count(*)"
	}
	query := fmt.Sprintf("select %s from %s ", columnSelection, tableName)
	qualifications := filter.Qualification

	if len(qualifications) > 0 {
		query += fmt.Sprintf(" where ")
	}

	isConditionAppednded := false
	for index, qualification := range qualifications {
		if index != 0 && !isConditionAppednded {
			query += " OR "
		}

		isConditionAppednded = false
		column := qualification.Column
		column = common.ToSnakeCase(column)
		operator := qualification.Operator
		value := qualification.Value

		if "os" == strings.ToLower(column) {
			var osType common.OsType
			os, err := osType.ToOsType(strings.ToLower(value))
			if err == nil {
				query += " \"os\" = " + fmt.Sprintf("%d", os) + " "
			}
		} else if strings.EqualFold("contains", strings.ToLower(operator)) {
			query += "\"" + column + "\" ILIKE '%" + value + "%' "
		} else if strings.EqualFold("not_contains", strings.ToLower(qualification.Operator)) {
			query += "\"" + column + "\" NOT ILIKE '%" + value + "%' "
		} else if strings.EqualFold("equals", strings.ToLower(operator)) {
			query += "\"" + column + "\" = '" + value + "' "
		} else if strings.EqualFold("not_equals", strings.ToLower(qualification.Operator)) {
			query += "\"" + column + "\" != '" + value + "' "
		} else if strings.EqualFold("in", strings.ToLower(operator)) {
			query += "\"" + column + "\" IN (" + value + ") "
		} else if strings.EqualFold("not_in", strings.ToLower(operator)) {
			query += "\"" + column + "\" Not IN (" + value + ") "
		} else if strings.EqualFold(">=", strings.ToLower(operator)) {
			query += "\"" + column + "\" >= '" + value + "' "
		} else if strings.EqualFold("<=", strings.ToLower(operator)) {
			query += "\"" + column + "\" <= '" + value + "' "
		}

		if index != len(qualifications)-1 && "" != qualification.Condition {
			isConditionAppednded = true
			query += qualification.Condition + " "
		}
	}

	if queryCondition != "" {
		query += " AND " + queryCondition
	}

	if !isCountQual {
		if filter.SortBy != "" {
			sortBy := strings.Split(filter.SortBy, ",")
			var sortClauses []string
			for _, sortByValue := range sortBy {
				direction := "DESC"
				if strings.Contains(sortByValue, "-") {
					direction = "ASC"
					sortByValue = strings.ReplaceAll(sortByValue, "-", "")
				}
				sortByValue = common.ToSnakeCase(sortByValue)
				sortClauses = append(sortClauses, fmt.Sprintf("\"%s\" %s", sortByValue, direction))
			}
			if len(sortClauses) > 0 {
				query += "order by " + strings.Join(sortClauses, ", ") + " "
			}
		} else {
			query += fmt.Sprintf("order by \"%s\" %s ", "id", "DESC")
		}

		if filter.Size > 0 {
			query += fmt.Sprintf("offset %v limit %v ", filter.Offset, filter.Size)
		}
	}
	query += ";"
	logger.ServiceLogger.Debug("query", query)
	return query
}

func ConvertJsonToSearchFilter(w http.ResponseWriter, r *http.Request, searchFilter SearchFilter) (SearchFilter, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &searchFilter)
	v := validator.New()
	err = v.Struct(searchFilter)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			fmt.Fprintf(w, string(jsonData))
			break
		}
	}
	return searchFilter, err
}
