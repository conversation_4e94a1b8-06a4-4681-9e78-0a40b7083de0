package rest

type UbuntuPatchRest struct {
	BaseEntityRest
	UUID              string `json:"uuid"`
	OsVersion         string `json:"osVersion"`
	Channel           string `json:"channel"`
	Repo              string `json:"repo"`
	PackageName       string `json:"packageName"`
	Arch              string `json:"arch"`
	Version           string `json:"version"`
	Priority          string `json:"priority"`
	Section           string `json:"section"`
	Origin            string `json:"origin"`
	Depends           string `json:"depends"`
	Breaks            string `json:"breaks"`
	FileName          string `json:"fileName"`
	DownloadUrl       string `json:"downloadUrl"`
	Size              int64  `json:"size"`
	Sha1              string `json:"Sha1"`
	PkgAndVersion     string `json:"pkgAndVersion"`
	PkgNameWithDistro string `json:"pkgNameWithDistro"`
	Downloadable      bool   `json:"downloadable"`
	ReleaseDate       int64  `json:"releaseDate"`
}
