package repository

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/thirdparty"
)

type ThirdPartyPackageRepository struct {
	dbConnection *bun.DB
}

func NewThirdPartyPackageRepository() *ThirdPartyPackageRepository {
	return &ThirdPartyPackageRepository{
		dbConnection: db.Connection,
	}
}

func (repo ThirdPartyPackageRepository) Create(pkg *thirdparty.ThirdPartyPackage) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(pkg).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository Create]", err.Error())
		return 0, err
	}
	return pkg.Id, nil
}

func (repo ThirdPartyPackageRepository) Update(pkg *thirdparty.ThirdPartyPackage) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository Update]", err.Error())
		return 0, err
	}
	return pkg.Id, nil
}

func (repo ThirdPartyPackageRepository) GetById(pkgId int64) (thirdparty.ThirdPartyPackage, error) {
	var pkg thirdparty.ThirdPartyPackage
	err := repo.dbConnection.NewSelect().Model(&pkg).Where("id = ?", pkgId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository GetById]", err.Error())
		return pkg, err
	}
	return pkg, nil
}

func (repo ThirdPartyPackageRepository) PermanentDeleteById(pkgId int64) (bool, error) {
	pkg := new(thirdparty.ThirdPartyPackage)
	pkg.Id = pkgId
	_, err := repo.dbConnection.NewDelete().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository PermanentDeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo ThirdPartyPackageRepository) GetAllPackage(query string) ([]thirdparty.ThirdPartyPackage, error) {
	var pkg []thirdparty.ThirdPartyPackage
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &pkg)
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository GetAllPackage]", err.Error())
		return pkg, err
	}
	return pkg, nil
}

func (repo ThirdPartyPackageRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo ThirdPartyPackageRepository) GetPkgCountByLatestVersion(latestVersion string, applicationType int) (int, error) {
	var pkg thirdparty.ThirdPartyPackage
	count, err := repo.dbConnection.NewSelect().Model(&pkg).Where("version = ?", latestVersion).Where("application = ?", applicationType).Count(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository GetPkgCountByLatestVersion]", err.Error())
		return 0, err
	}
	return count, nil
}

func (repo ThirdPartyPackageRepository) GetPkgByOsVersionPlatformOsArchApplication(osVersion string, arch, platform, applicationType int) (thirdparty.ThirdPartyPackage, error) {
	var pkg thirdparty.ThirdPartyPackage
	err := repo.dbConnection.NewSelect().Model(&pkg).
		Where("os_version = ?", osVersion).
		Where("application = ?", applicationType).
		Where("os = ?", platform).
		Where("arch = ?", arch).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository GetPkgByOsVersionPlatformOsArchApplication]", err.Error())
		return pkg, err
	}
	return pkg, nil
}

func (repo ThirdPartyPackageRepository) GetPkgByPlatformOsArchApplication(arch, platform, applicationType int) (thirdparty.ThirdPartyPackage, error) {
	var pkg thirdparty.ThirdPartyPackage
	err := repo.dbConnection.NewSelect().Model(&pkg).Where("application = ?", applicationType).
		Where("os = ?", platform).
		Where("arch = ?", arch).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository GetPkgByPlatformOsArchApplication]", err.Error())
		return pkg, err
	}
	return pkg, nil
}

func (repo ThirdPartyPackageRepository) DeletePatch(pkg thirdparty.ThirdPartyPackage) (int64, error) {
	result, err := repo.dbConnection.NewDelete().Model(&pkg).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository DeletePatch]", err.Error())
		return 0, err
	}
	return result.RowsAffected()
}

func (repo ThirdPartyPackageRepository) GetCountByUuid(uuid string) int {
	var pkg thirdparty.ThirdPartyPackage
	count, err := repo.dbConnection.NewSelect().Model(&pkg).
		Where("uuid = ?", uuid).
		Count(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository GetCountByUuid]", err.Error())
		return 0
	}
	return count
}

func (repo ThirdPartyPackageRepository) GetByUUId(uuid string) thirdparty.ThirdPartyPackage {
	var pkg thirdparty.ThirdPartyPackage
	err := repo.dbConnection.NewSelect().Model(&pkg).
		Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository GetByUUId]", err.Error())
	}
	return pkg
}

func (repo ThirdPartyPackageRepository) DeletePatchByUUIDLike(like string) {
	_, err := repo.dbConnection.NewDelete().Model(&thirdparty.ThirdPartyPackage{}).
		Where("uuid LIKE ?", like).
		Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[ThirdPartyPackageRepository DeletePatchByUUIDLike]", err.Error())
	}

}
