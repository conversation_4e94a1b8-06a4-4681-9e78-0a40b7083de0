package repository

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
)

type PatchProductRepository struct {
	dbConnection *bun.DB
}

func NewPatchProductRepository() *PatchProductRepository {
	return &PatchProductRepository{
		dbConnection: db.Connection,
	}
}

func (repo PatchProductRepository) Create(product *windows.PatchProduct) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(product).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchProductRepository Create]", err.Error())
		return 0, err
	}
	return product.Id, nil
}

func (repo PatchProductRepository) Update(product *windows.PatchProduct) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(product).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchProductRepository Update]", err.Error())
		return 0, err
	}
	return product.Id, nil
}

func (repo PatchProductRepository) GetById(productId int64) (windows.PatchProduct, error) {
	var product windows.PatchProduct
	err := repo.dbConnection.NewSelect().Model(&product).Where("id = ?", productId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchProductRepository GetById]", err.Error())
		return product, err
	}
	return product, nil
}

func (repo PatchProductRepository) FindByUUID(uuid string) (windows.PatchProduct, error) {
	var product windows.PatchProduct
	err := repo.dbConnection.NewSelect().Model(&product).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchProductRepository FindByUUID]", err.Error())
		return product, err
	}
	return product, nil
}

func (repo PatchProductRepository) DeleteById(productId int64) (bool, error) {
	product := new(windows.PatchProduct)
	product.Id = productId
	_, err := repo.dbConnection.NewDelete().Model(product).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchProductRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo PatchProductRepository) GetAllProducts(query string) ([]windows.PatchProduct, error) {
	var product []windows.PatchProduct
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &product)
	if err != nil {
		logger.ServiceLogger.Error("[PatchProductRepository GetAllProducts]", err.Error())
		return product, err
	}
	return product, nil
}

func (repo PatchProductRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[PatchProductRepository Count]", err.Error())
		return 0
	}
	return count
}
