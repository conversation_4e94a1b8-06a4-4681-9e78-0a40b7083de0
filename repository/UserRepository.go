package repository

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model"
)

type UserRepository struct {
	dbConnection *bun.DB
}

func NewUserRepository() *UserRepository {
	return &UserRepository{
		dbConnection: db.Connection,
	}
}

func (repo UserRepository) Create(usr *model.User) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(usr).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UserRepository Create]", err.Error())
		return 0, err
	}
	return usr.Id, nil
}

func (repo UserRepository) Update(usr *model.User) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(usr).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UserRepository Update]", err.Error())
		return 0, err
	}
	return usr.Id, nil
}

func (repo UserRepository) GetById(usrId int64, includeArchive bool) (model.User, error) {
	var usr model.User
	var err error
	err = repo.dbConnection.NewSelect().Model(&usr).Where("id = ?", usrId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UserRepository GetById]", err.Error())
		return usr, err
	}
	return usr, nil
}

func (repo UserRepository) PermanentDeleteById(pkgId int64) (bool, error) {
	pkg := new(model.User)
	pkg.Id = pkgId
	_, err := repo.dbConnection.NewDelete().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UserRepository PermanentDeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo UserRepository) GetAllUser(query string) ([]model.User, error) {
	var usr []model.User
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &usr)
	if err != nil {
		logger.ServiceLogger.Error("[UserRepository GetAllUser]", err.Error())
		return usr, err
	}
	return usr, nil
}

func (repo UserRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[UserRepository Count]", err.Error())
		return 0
	}
	return count
}
