package ubutnu

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/ubuntu"
)

type UbuntuNoticeDataRepository struct {
	dbConnection *bun.DB
}

func NewUbuntuNoticeDataRepository() *UbuntuNoticeDataRepository {
	return &UbuntuNoticeDataRepository{
		dbConnection: db.Connection,
	}
}

func (repo UbuntuNoticeDataRepository) Create(ubuntuNoticeData *ubuntu.UbuntuNoticeData) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(ubuntuNoticeData).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeDataRepository Create]", err.Error())
		return 0, err
	}
	return ubuntuNoticeData.Id, nil
}

func (repo UbuntuNoticeDataRepository) Update(ubuntuNoticeData *ubuntu.UbuntuNoticeData) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(ubuntuNoticeData).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeDataRepository Update]", err.Error())
		return 0, err
	}
	return ubuntuNoticeData.Id, nil
}

func (repo UbuntuNoticeDataRepository) GetById(id int64) (ubuntu.UbuntuNoticeData, error) {
	var ubuntuNoticeData ubuntu.UbuntuNoticeData
	err := repo.dbConnection.NewSelect().Model(&ubuntuNoticeData).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeDataRepository GetById]", err.Error())
		return ubuntuNoticeData, err
	}
	return ubuntuNoticeData, nil
}

func (repo UbuntuNoticeDataRepository) DeleteById(id int64) (bool, error) {
	ubuntuNoticeData := ubuntu.UbuntuNoticeData{}
	ubuntuNoticeData.Id = id
	_, err := repo.dbConnection.NewDelete().Model(ubuntuNoticeData).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeDataRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo UbuntuNoticeDataRepository) GetAllUbuntuNoticeData(query string) ([]ubuntu.UbuntuNoticeData, error) {
	var ubuntuNoticeData []ubuntu.UbuntuNoticeData
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &ubuntuNoticeData)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeDataRepository GetAllUbuntuNoticeData]", err.Error())
		return ubuntuNoticeData, err
	}
	return ubuntuNoticeData, nil
}

func (repo UbuntuNoticeDataRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeDataRepository Count]", err.Error())
		return 0
	}
	return count
}
