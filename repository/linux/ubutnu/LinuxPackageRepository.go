package ubutnu

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/ubuntu"
)

type LinuxPackageRepository struct {
	dbConnection *bun.DB
}

func NewLinuxPackageRepository() *LinuxPackageRepository {
	return &LinuxPackageRepository{
		dbConnection: db.Connection,
	}
}

func (repo LinuxPackageRepository) Create(linuxPackage *ubuntu.LinuxPackage) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(linuxPackage).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LinuxPackageRepository Create]", err.Error())
		return 0, err
	}
	return linuxPackage.Id, nil
}

func (repo LinuxPackageRepository) Update(linuxPackage *ubuntu.LinuxPackage) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(linuxPackage).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LinuxPackageRepository Update]", err.Error())
		return 0, err
	}
	return linuxPackage.Id, nil
}

func (repo LinuxPackageRepository) GetById(id int64) (ubuntu.LinuxPackage, error) {
	var linuxPackage ubuntu.LinuxPackage
	err := repo.dbConnection.NewSelect().Model(&linuxPackage).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LinuxPackageRepository GetById]", err.Error())
		return linuxPackage, err
	}
	return linuxPackage, nil
}

func (repo LinuxPackageRepository) FindByNameAndDistributionAndArch(name, version, distribution, arch string) ([]ubuntu.LinuxPackage, error) {
	var linuxPackages []ubuntu.LinuxPackage
	err := repo.dbConnection.NewSelect().Model(&linuxPackages).
		Where("name = ?", name).
		Where("version = ?", version).
		Where("distribution = ?", distribution).
		Where("arch = ?", arch).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LinuxPackageRepository FindByNameAndDistributionAndArch]", err.Error())
		return nil, err
	}
	return linuxPackages, nil
}

func (repo LinuxPackageRepository) DeleteById(id int64) (bool, error) {
	linuxPackage := ubuntu.LinuxPackage{}
	linuxPackage.Id = id
	_, err := repo.dbConnection.NewDelete().Model(linuxPackage).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LinuxPackageRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo LinuxPackageRepository) GetAllLinuxPackages(query string) ([]ubuntu.LinuxPackage, error) {
	var linuxPackages []ubuntu.LinuxPackage
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &linuxPackages)
	if err != nil {
		logger.ServiceLogger.Error("[LinuxPackageRepository GetAllLinuxPackages]", err.Error())
		return linuxPackages, err
	}
	return linuxPackages, nil
}

func (repo LinuxPackageRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[LinuxPackageRepository Count]", err.Error())
		return 0
	}
	return count
}
