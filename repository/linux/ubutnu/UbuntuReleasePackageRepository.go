package ubutnu

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/ubuntu"
)

type UbuntuReleasePackageRepository struct {
	dbConnection *bun.DB
}

func NewUbuntuReleasePackageRepository() *UbuntuReleasePackageRepository {
	return &UbuntuReleasePackageRepository{
		dbConnection: db.Connection,
	}
}

func (repo UbuntuReleasePackageRepository) Create(ubuntuReleasePackage *ubuntu.UbuntuReleasePackage) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(ubuntuReleasePackage).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuReleasePackageRepository Create]", err.Error())
		return 0, err
	}
	return ubuntuReleasePackage.Id, nil
}

func (repo UbuntuReleasePackageRepository) Update(ubuntuReleasePackage *ubuntu.UbuntuReleasePackage) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(ubuntuReleasePackage).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuReleasePackageRepository Update]", err.Error())
		return 0, err
	}
	return ubuntuReleasePackage.Id, nil
}

func (repo UbuntuReleasePackageRepository) GetById(id int64) (ubuntu.UbuntuReleasePackage, error) {
	var ubuntuReleasePackage ubuntu.UbuntuReleasePackage
	err := repo.dbConnection.NewSelect().Model(&ubuntuReleasePackage).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuReleasePackageRepository GetById]", err.Error())
		return ubuntuReleasePackage, err
	}
	return ubuntuReleasePackage, nil
}

func (repo UbuntuReleasePackageRepository) DeleteById(id int64) (bool, error) {
	ubuntuReleasePackage := ubuntu.UbuntuReleasePackage{}
	ubuntuReleasePackage.Id = id
	_, err := repo.dbConnection.NewDelete().Model(ubuntuReleasePackage).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuReleasePackageRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo UbuntuReleasePackageRepository) GetAllUbuntuReleasePackages(query string) ([]ubuntu.UbuntuReleasePackage, error) {
	var ubuntuReleasePackages []ubuntu.UbuntuReleasePackage
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &ubuntuReleasePackages)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuReleasePackageRepository GetAllUbuntuReleasePackages]", err.Error())
		return ubuntuReleasePackages, err
	}
	return ubuntuReleasePackages, nil
}

func (repo UbuntuReleasePackageRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuReleasePackageRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo UbuntuReleasePackageRepository) FindByNameAndVersion(nameVersion string) ([]ubuntu.UbuntuReleasePackage, error) {
	var ubuntuReleasePackages []ubuntu.UbuntuReleasePackage
	err := repo.dbConnection.NewSelect().Model(&ubuntuReleasePackages).
		Where("name_and_version = ?", nameVersion).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuReleasePackageRepository FindByNameAndVersion]", err.Error())
		return nil, err
	}
	return ubuntuReleasePackages, nil
}
