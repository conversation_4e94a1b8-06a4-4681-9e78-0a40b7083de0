package ubutnu

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/ubuntu"
)

type UbuntuNoticeHistoryRepository struct {
	dbConnection *bun.DB
}

func NewUbuntuNoticeHistoryRepository() *UbuntuNoticeHistoryRepository {
	return &UbuntuNoticeHistoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo UbuntuNoticeHistoryRepository) Create(ubuntuNoticeHistory *ubuntu.UbuntuNoticeHistory) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(ubuntuNoticeHistory).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeHistoryRepository Create]", err.Error())
		return 0, err
	}
	return ubuntuNoticeHistory.Id, nil
}

func (repo UbuntuNoticeHistoryRepository) Update(ubuntuNoticeHistory *ubuntu.UbuntuNoticeHistory) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(ubuntuNoticeHistory).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeHistoryRepository Update]", err.Error())
		return 0, err
	}
	return ubuntuNoticeHistory.Id, nil
}

func (repo UbuntuNoticeHistoryRepository) GetById(id int64) (ubuntu.UbuntuNoticeHistory, error) {
	var ubuntuNoticeHistory ubuntu.UbuntuNoticeHistory
	err := repo.dbConnection.NewSelect().Model(&ubuntuNoticeHistory).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeHistoryRepository GetById]", err.Error())
		return ubuntuNoticeHistory, err
	}
	return ubuntuNoticeHistory, nil
}

func (repo UbuntuNoticeHistoryRepository) DeleteById(id int64) (bool, error) {
	ubuntuNoticeHistory := ubuntu.UbuntuNoticeHistory{}
	ubuntuNoticeHistory.Id = id
	_, err := repo.dbConnection.NewDelete().Model(ubuntuNoticeHistory).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeHistoryRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo UbuntuNoticeHistoryRepository) GetAllUbuntuNoticeHistory(query string) ([]ubuntu.UbuntuNoticeHistory, error) {
	var ubuntuNoticeHistories []ubuntu.UbuntuNoticeHistory
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &ubuntuNoticeHistories)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeHistoryRepository GetAllUbuntuNoticeHistory]", err.Error())
		return ubuntuNoticeHistories, err
	}
	return ubuntuNoticeHistories, nil
}

func (repo UbuntuNoticeHistoryRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeHistoryRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo UbuntuNoticeHistoryRepository) FindFirstByOrderByTotalSizeDescCreatedTimeDesc() (ubuntu.UbuntuNoticeHistory, error) {
	var ubuntuNoticeHistory ubuntu.UbuntuNoticeHistory
	err := repo.dbConnection.NewSelect().Model(&ubuntuNoticeHistory).
		Order("created_time DESC").
		Limit(1).
		Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuNoticeHistoryRepository FindFirstByOrderByTotalSizeDescCreatedTimeDesc]", err.Error())
		return ubuntuNoticeHistory, err
	}
	return ubuntuNoticeHistory, nil
}
