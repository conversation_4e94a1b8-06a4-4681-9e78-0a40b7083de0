package ubutnu

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/ubuntu"
)

type UbuntuPatchRepository struct {
	dbConnection *bun.DB
}

func NewUbuntuPatchRepository() *UbuntuPatchRepository {
	return &UbuntuPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo UbuntuPatchRepository) Create(ubuntuPatch *ubuntu.UbuntuPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(ubuntuPatch).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository Create]", err.Error())
		return 0, err
	}
	return ubuntuPatch.Id, nil
}

func (repo UbuntuPatchRepository) Update(ubuntuPatch *ubuntu.UbuntuPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(ubuntuPatch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository Update]", err.Error())
		return 0, err
	}
	return ubuntuPatch.Id, nil
}

func (repo UbuntuPatchRepository) GetById(id int64) (ubuntu.UbuntuPatch, error) {
	var ubuntuPatch ubuntu.UbuntuPatch
	err := repo.dbConnection.NewSelect().Model(&ubuntuPatch).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository GetById]", err.Error())
		return ubuntuPatch, err
	}
	return ubuntuPatch, nil
}

func (repo UbuntuPatchRepository) FindByUUID(uuid string) (ubuntu.UbuntuPatch, error) {
	var ubuntuPatch ubuntu.UbuntuPatch
	err := repo.dbConnection.NewSelect().Model(&ubuntuPatch).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository FindByUUID]", err.Error())
		return ubuntuPatch, err
	}
	return ubuntuPatch, nil
}

func (repo UbuntuPatchRepository) DeleteById(id int64) (bool, error) {
	ubuntuPatch := ubuntu.UbuntuPatch{}
	ubuntuPatch.Id = id
	_, err := repo.dbConnection.NewDelete().Model(ubuntuPatch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo UbuntuPatchRepository) GetAllUbuntuPatches(query string) ([]ubuntu.UbuntuPatch, error) {
	var ubuntuPatches []ubuntu.UbuntuPatch
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &ubuntuPatches)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository GetAllUbuntuPatches]", err.Error())
		return ubuntuPatches, err
	}
	return ubuntuPatches, nil
}

func (repo UbuntuPatchRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo UbuntuPatchRepository) GetAllUuidByCreatedTime(createdTime int64) ([]string, error) {
	var uuids []string
	err := repo.dbConnection.NewSelect().Model(&ubuntu.UbuntuPatch{}).
		Column("uuid").
		Where("created_time > ?", createdTime).
		Scan(context.Background(), &uuids)
	if err != nil {
		logger.ServiceLogger.Error("[UbuntuPatchRepository GetAllUuidByCreatedTime]", err.Error())
		return nil, err
	}
	return uuids, nil
}
