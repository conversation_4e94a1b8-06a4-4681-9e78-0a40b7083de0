package repository

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
)

type PatchCategoryRepository struct {
	dbConnection *bun.DB
}

func NewPatchCategoryRepository() *PatchCategoryRepository {
	return &PatchCategoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo PatchCategoryRepository) Create(category *windows.PatchCategory) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(category).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository Create]", err.Error())
		return 0, err
	}
	return category.Id, nil
}

func (repo PatchCategoryRepository) Update(category *windows.PatchCategory) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(category).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository Update]", err.Error())
		return 0, err
	}
	return category.Id, nil
}

func (repo PatchCategoryRepository) GetById(categoryId int64) (windows.PatchCategory, error) {
	var category windows.PatchCategory
	err := repo.dbConnection.NewSelect().Model(&category).Where("id = ?", categoryId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository GetById]", err.Error())
		return category, err
	}
	return category, nil
}

func (repo PatchCategoryRepository) FindByUUID(uuid string) (windows.PatchCategory, error) {
	var category windows.PatchCategory
	err := repo.dbConnection.NewSelect().Model(&category).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository FindByUUID]", err.Error())
		return category, err
	}
	return category, nil
}

func (repo PatchCategoryRepository) FindByUUIDsIn(uuids []string) ([]windows.PatchCategory, error) {
	var categories []windows.PatchCategory
	err := repo.dbConnection.NewSelect().Model(&categories).Where("uuid IN (?)", bun.In(uuids)).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository FindByUUIDsIn]", err.Error())
		return nil, err
	}
	return categories, nil
}

func (repo PatchCategoryRepository) DeleteById(categoryId int64) (bool, error) {
	category := new(windows.PatchCategory)
	category.Id = categoryId
	_, err := repo.dbConnection.NewDelete().Model(category).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo PatchCategoryRepository) GetAllCategory(query string) ([]windows.PatchCategory, error) {
	var category []windows.PatchCategory
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &category)
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository GetAllCategory]", err.Error())
		return category, err
	}
	return category, nil
}

func (repo PatchCategoryRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[PatchCategoryRepository Count]", err.Error())
		return 0
	}
	return count
}
