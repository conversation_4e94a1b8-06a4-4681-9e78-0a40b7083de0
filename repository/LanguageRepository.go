package repository

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
)

type LanguageRepository struct {
	dbConnection *bun.DB
}

func NewLanguageRepository() *LanguageRepository {
	return &LanguageRepository{
		dbConnection: db.Connection,
	}
}

func (repo LanguageRepository) Create(language *windows.Language) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(language).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LanguageRepository Create]", err.Error())
		return 0, err
	}
	return language.Id, nil
}

func (repo LanguageRepository) Update(language *windows.Language) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(language).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LanguageRepository Update]", err.Error())
		return 0, err
	}
	return language.Id, nil
}

func (repo LanguageRepository) GetAllLanguage(query string) ([]windows.Language, error) {
	var language []windows.Language
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &language)
	if err != nil {
		logger.ServiceLogger.Error("[LanguageRepository GetAllLanguage]", err.Error())
		return language, err
	}
	return language, nil
}

func (repo LanguageRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[LanguageRepository Count]", err)
		return 0
	}
	return count
}

func (repo LanguageRepository) FindByLanguageCode(code int64) (windows.Language, error) {
	var language windows.Language
	err := repo.dbConnection.NewSelect().Model(&language).Where("code = ?", code).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[LanguageRepository FindByLanguageCode]", err)
		return language, err
	}
	return language, nil
}
