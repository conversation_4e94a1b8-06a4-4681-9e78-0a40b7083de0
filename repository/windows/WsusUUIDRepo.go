package windows

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
)

type WsusUUIDRepository struct {
	dbConnection *bun.DB
}

func NewWsusUUIDRepository() *WsusUUIDRepository {
	return &WsusUUIDRepository{
		dbConnection: db.Connection,
	}
}

func (repo WsusUUIDRepository) DeleteAll() bool {
	_, err := repo.dbConnection.NewDelete().Model(&windows.WsusUUID{}).Where("1=1").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WsusUUIDRepository DeleteAll]", err.Error())
	}
	return err == nil
}

func (repo WsusUUIDRepository) ResetSequence() {
	_, err := repo.dbConnection.Exec("ALTER SEQUENCE wsus_uuids_id_seq RESTART WITH 1")
	if err != nil {
		logger.ServiceLogger.Error("[WsusUUIDRepository ResetSequence]", err.Error())
	}
}

func (repo *WsusUUIDRepository) SaveAll(records []windows.WsusUUID) {
	for _, record := range records {
		if _, err := repo.dbConnection.NewInsert().Model(&record).Exec(context.Background()); err != nil {
			logger.ServiceLogger.Error("[WsusUUIDRepository SaveAll]", err.Error())
			continue
		}
	}
}
