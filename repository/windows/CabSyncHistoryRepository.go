package windows

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
)

type CabSyncHistoryRepository struct {
	dbConnection *bun.DB
}

func NewCabSyncHistoryRepository() *CabSyncHistoryRepository {
	return &CabSyncHistoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo CabSyncHistoryRepository) Create(history *windows.CabSyncHistory) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(history).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CabSyncHistoryRepository Create]", err.Error())
		return 0, err
	}
	return history.Id, nil
}

func (repo CabSyncHistoryRepository) Update(history *windows.CabSyncHistory) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(history).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CabSyncHistoryRepository Update]", err.Error())
		return 0, err
	}
	return history.Id, nil
}

func (repo CabSyncHistoryRepository) GetByReleaseDate(releaseDate string) (windows.CabSyncHistory, error) {
	var history windows.CabSyncHistory
	err := repo.dbConnection.NewSelect().Model(&history).Where("release_date = ?", releaseDate).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CabSyncHistoryRepository GetById]", err.Error())
		return history, err
	}
	return history, nil
}

func (repo CabSyncHistoryRepository) GetAllCabHistory(query string) ([]windows.CabSyncHistory, error) {
	var patches []windows.CabSyncHistory
	if query == "" {
		err := repo.dbConnection.NewSelect().Model(&patches).Order("release_date DESC").Limit(36).Scan(context.Background(), &patches)
		if err != nil {
			logger.ServiceLogger.Error("[CabSyncHistoryRepository GetAllCabHistory]", err.Error())
			return patches, err
		}
	} else {
		err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &patches)
		if err != nil {
			logger.ServiceLogger.Error("[CabSyncHistoryRepository GetAllCabHistory]", err.Error())
			return patches, err
		}
	}

	return patches, nil
}

func (repo CabSyncHistoryRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[CabSyncHistoryRepository Count]", err.Error())
		return 0
	}
	return count
}
