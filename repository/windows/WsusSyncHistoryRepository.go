package windows

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
)

type WsusSyncHistoryRepository struct {
	dbConnection *bun.DB
}

func NewWsusSyncHistoryRepository() *WsusSyncHistoryRepository {
	return &WsusSyncHistoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo WsusSyncHistoryRepository) Create(history *windows.WsusSyncHistory) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(history).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WsusSyncHistoryRepository Create]", err.Error())
		return 0, err
	}
	return history.Id, nil
}

func (repo WsusSyncHistoryRepository) FindFirstByOrderByUpdateTimeOfLastRecordDesc() (windows.WsusSyncHistory, error) {
	var history windows.WsusSyncHistory
	err := repo.dbConnection.NewSelect().Model(&history).Order("update_time_of_last_record DESC").Limit(1).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WsusSyncHistoryRepository FindFirstByOrderByUpdateTimeOfLastRecordDesc]", err.Error())
		return history, err
	}
	return history, nil
}
