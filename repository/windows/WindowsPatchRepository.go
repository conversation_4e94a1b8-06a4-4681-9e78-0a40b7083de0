package windows

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
	"strings"
	"time"
)

type WindowsPatchRepository struct {
	dbConnection *bun.DB
}

func NewWindowsPatchRepository() *WindowsPatchRepository {
	return &WindowsPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo WindowsPatchRepository) Create(patch *windows.WindowsPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patch).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository Create]", err.Error())
		return 0, err
	}
	return patch.Id, nil
}

func (repo WindowsPatchRepository) Update(patch *windows.WindowsPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository Update]", err.Error())
		return 0, err
	}
	return patch.Id, nil
}

func (repo WindowsPatchRepository) GetById(patchId int64) (windows.WindowsPatch, error) {
	var patch windows.WindowsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("id = ?", patchId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository GetById]", err.Error())
		return patch, err
	}
	return patch, nil
}

func (repo WindowsPatchRepository) GetByUUID(uuid string) (windows.WindowsPatch, error) {
	var patch windows.WindowsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository GetByUUID]", err.Error())
		return patch, err
	}
	return patch, nil
}

func (repo WindowsPatchRepository) FindByUUID(uuid string) (windows.WindowsPatch, error) {
	var patch windows.WindowsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("uuid = ?", uuid).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository FindByUUID]", err.Error())
		return patch, err
	}
	return patch, nil
}

func (repo WindowsPatchRepository) FindByKBId(kbId string) (windows.WindowsPatch, error) {
	var patch windows.WindowsPatch
	err := repo.dbConnection.NewSelect().Model(&patch).Where("kb_id = ?", kbId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository FindByKBId]", err.Error())
		return patch, err
	}
	return patch, nil
}

func (repo WindowsPatchRepository) DeleteById(patchId int64) (bool, error) {
	patch := new(windows.WindowsPatch)
	patch.Id = patchId
	_, err := repo.dbConnection.NewDelete().Model(patch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo WindowsPatchRepository) GetAllWindowsPatch(query string) ([]windows.WindowsPatch, error) {
	var patches []windows.WindowsPatch
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &patches)
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository GetAllWindowsPatch]", err.Error())
		return patches, err
	}
	return patches, nil
}

func (repo WindowsPatchRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository Count]", err.Error())
		return 0
	}
	return count
}

func (repo WindowsPatchRepository) UpdateKbIDByUUID(uuid string, kbID string) error {
	_, err := repo.dbConnection.NewUpdate().Model(&windows.WindowsPatch{}).
		Set("kb_id = ?", kbID).
		Set("updated_time = ?", time.Now().UnixMilli()).
		Where("uuid = ?", uuid).
		Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository UpdateKbIDByUUID]", err.Error())
		return err
	}
	return nil
}

func (repo WindowsPatchRepository) GetAllUuidByCreatedTime(createdTime int64) ([]string, error) {
	var uuids []string
	err := repo.dbConnection.NewSelect().Model(&windows.WindowsPatch{}).
		Column("uuid").
		Where("created_time > ?", createdTime).
		Scan(context.Background(), &uuids)
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository GetAllUuidByCreatedTime]", err.Error())
		return nil, err
	}
	return uuids, nil
}

func (repo WindowsPatchRepository) RunMissingKbFromWindowsPatchQuery() ([]string, error) {
	var results []string
	query := "SELECT lower(x.uuid)\n        FROM public.wsus_uuids x\n        EXCEPT\n        SELECT lower(kb.uuid)\n        FROM public.windows_patches kb"
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &results)
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository RunMissingKbFromWindowsPatchQuery]", err.Error())
	}
	return results, err
}

func (repo *WindowsPatchRepository) AllPatchListTxt(category, arch string) []string {
	var results []string
	categoryQuery := "SELECT c.uuid FROM patch_categories c WHERE c.name LIKE '{$category}'"
	/*if strings.Contains(category, ",") {
		cat := strings.Split(category, ",")
		if len(cat) == 2 {
			categoryQuery = "SELECT c.uuid FROM patch_categories c WHERE c.name LIKE '{$cat1}%' and c.name LIKE '%{$cat2}' limit 1 offset 0"
			categoryQuery = strings.ReplaceAll(categoryQuery, "{$cat1}", cat[0])
			categoryQuery = strings.ReplaceAll(categoryQuery, "{$cat2}", cat[1])
		} else {
			categoryQuery = strings.ReplaceAll(categoryQuery, "{$category}", category)
		}
	} else {
		categoryQuery = strings.ReplaceAll(categoryQuery, "{$category}", category)
	}*/

	categoryQuery = strings.ReplaceAll(categoryQuery, "{$category}", category)
	// Prepare the query with placeholders
	query := `SELECT 
        CONCAT(
            w.uuid, ',', 
            w.classification, ',', 
            w.created_time, ',"', 
            w.kb_id_to_be_installed, '"'
        ) AS concatenated_fields
    FROM 
        windows_patches w
    WHERE 
        (w.publish_state IS NULL OR w.publish_state = 'Published')
        AND EXISTS (
            SELECT *
            FROM jsonb_array_elements_text(products_uuid) AS elem
            WHERE elem in ({$category})
        )
        AND (LOWER(w.os_arch) LIKE '{$arch}' OR w.os_arch IS NULL OR w.os_arch = '')
    ORDER BY 
        w.created_time ASC;`
	query = strings.ReplaceAll(query, "{$arch}", arch)
	query = strings.ReplaceAll(query, "{$category}", categoryQuery)

	// Use the dbConnection to run the query with parameters
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &results)
	if err != nil {
		logger.ServiceLogger.Error("[WindowsPatchRepository AllPatchListTxt]", err.Error())
		return results
	}

	if results == nil {
		logger.ServiceLogger.Info("query: ", query)
	}

	return results
}
