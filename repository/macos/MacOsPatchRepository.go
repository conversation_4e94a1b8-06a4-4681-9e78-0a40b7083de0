package macosrepo

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/macos"
)

type MacOsPatchRepository struct {
	dbConnection *bun.DB
}

func NewMacOsPatchRepository() *MacOsPatchRepository {
	return &MacOsPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo MacOsPatchRepository) Create(macOsPatch *macos.MacOsPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(macOsPatch).Returning("id").Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[MacOsPatchRepository Create]", err.Error())
		return 0, err
	}
	return macOsPatch.Id, nil
}

func (repo MacOsPatchRepository) Update(macOsPatch *macos.MacOsPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(macOsPatch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[MacOsPatchRepository Update]", err.Error())
		return 0, err
	}
	return macOsPatch.Id, nil
}

func (repo MacOsPatchRepository) GetById(id int64) (macos.MacOsPatch, error) {
	var macOsPatch macos.MacOsPatch
	err := repo.dbConnection.NewSelect().Model(&macOsPatch).Where("id = ?", id).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[MacOsPatchRepository GetById]", err.Error())
		return macOsPatch, err
	}
	return macOsPatch, nil
}

func (repo MacOsPatchRepository) GetByProductKey(productKey string) (macos.MacOsPatch, error) {
	var macOsPatch macos.MacOsPatch
	err := repo.dbConnection.NewSelect().Model(&macOsPatch).Where("product_key = ?", productKey).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[MacOsPatchRepository GetByProductKey]", err.Error())
		return macOsPatch, err
	}
	return macOsPatch, nil
}

func (repo MacOsPatchRepository) DeleteById(id int64) (bool, error) {
	macOsPatch := new(macos.MacOsPatch)
	macOsPatch.Id = id
	_, err := repo.dbConnection.NewDelete().Model(macOsPatch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[MacOsPatchRepository DeleteById]", err.Error())
		return false, err
	}
	return true, nil
}

func (repo MacOsPatchRepository) GetAllMacPatch(query string) ([]macos.MacOsPatch, error) {
	var macOsPatches []macos.MacOsPatch
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &macOsPatches)
	if err != nil {
		logger.ServiceLogger.Error("[MacOsPatchRepository GetAllMacPatch]", err.Error())
		return macOsPatches, err
	}
	return macOsPatches, nil
}

func (repo MacOsPatchRepository) Count(query string) int {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		logger.ServiceLogger.Error("[MacOsPatchRepository Count]", err.Error())
		return 0
	}
	return count
}
