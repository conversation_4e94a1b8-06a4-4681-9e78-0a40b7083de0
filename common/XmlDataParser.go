package common

import (
	"encoding/xml"
	"fmt"
	"patch-central-repo/logger"
	"strings"
)

func ParsePrerequisitesFromXmlData(xmlData string) []string {
	var uuidList []string
	decoder := xml.NewDecoder(strings.NewReader(xmlData))
	for {
		token, err := decoder.Token()
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			logger.ServiceLogger.Error(fmt.Sprintf("Error decoding XML: %v", err))
		}
		if token == nil {
			break
		}
		switch tok := token.(type) {
		case xml.StartElement:
			if tok.Name.Local == "Prerequisites" || tok.Name.Local == "BundledUpdates" {
				for {
					token, err = decoder.Token()
					if err != nil {
						if err.Error() == "EOF" {
							break
						}
						logger.ServiceLogger.Error(fmt.Sprintf("Error reading child element: %v", err))
					}
					if token == nil {
						break
					}

					switch child := token.(type) {
					case xml.StartElement:
						for _, attr := range child.Attr {
							if attr.Name.Local == "UpdateID" {
								uuidList = append(uuidList, attr.Value)
							}
						}
					case xml.EndElement:
						if (tok.Name.Local == "Prerequisites" || tok.Name.Local == "BundledUpdates") && child.Name.Local == tok.Name.Local {
							break
						}
					}
				}
			}
		}
	}
	return uuidList
}
