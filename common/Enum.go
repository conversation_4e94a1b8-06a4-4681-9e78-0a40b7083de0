package common

import (
	"errors"
	"fmt"
)

type OsArchitecture int

const (
	X64 OsArchitecture = iota + 1
	X86
	Amd64
	Arm64
	All
	NoArch
)

func (oa OsArchitecture) String() string {
	switch oa {
	case X64:
		return "X64"
	case X86:
		return "X86"
	case Amd64:
		return "Amd64"
	case Arm64:
		return "Arm64"
	case All:
		return "All"
	case NoArch:
		return "noarch"
	default:
		return "Unknown"
	}
}

func (oa OsArchitecture) ToOsArch(osArch string) (OsArchitecture, error) {
	switch osArch {
	case "X64":
		return X64, nil
	case "X86":
		return X86, nil
	case "Amd64":
		return Amd64, nil
	case "Arm64":
		return Arm64, nil
	case "All":
		return All, nil
	case "noarch":
		return NoArch, nil
	default:
		return OsArchitecture(0), errors.New(fmt.Sprintf("invalid os type value '%s'", osArch))
	}
}

type OsType int

const (
	Windows OsType = iota + 1
	Linux
	Ubuntu
	MacOS
)

func (os OsType) String() string {
	switch os {
	case Windows:
		return "windows"
	case Linux:
		return "linux"
	case Ubuntu:
		return "ubuntu"
	case MacOS:
		return "mac"
	default:
		return "Unknown"
	}
}

func (os OsType) ToOsType(osType string) (OsType, error) {
	switch osType {
	case "windows":
		return Windows, nil
	case "linux":
		return Linux, nil
	case "ubuntu":
		return Ubuntu, nil
	case "mac":
		return MacOS, nil
	default:
		return Windows, errors.New(fmt.Sprintf("invalid os type value '%s'", osType))
	}
}

type ThirdPartyApplication int

const (
	UNKNOWN_APP ThirdPartyApplication = iota + 1
	FIREFOX
	VLC
	ADOBE_ACROBAT
	ADOBE_ACROBAT_READER_MUI
	ADOBE_ACROBAT_READER
	CHROME
	MICROSOFT_OFFICE
	SEVEN_Z
	NOTEPAD_PLUS_PLUS
	WINRAR
	VISUAL_STUDIO_CODE
	POSTMAN
	DBEAVER
	ANYDESK
	TEAMVIEWER
	ZOOM
	REALVNC_VIEWER
	POSTGRESQL
)

func (app ThirdPartyApplication) String() string {
	switch app {
	case FIREFOX:
		return "firefox"
	case VLC:
		return "vlc"
	case ADOBE_ACROBAT_READER_MUI:
		return "adobe_acrobat_reader_mui"
	case ADOBE_ACROBAT_READER:
		return "adobe_acrobat_reader"
	case ADOBE_ACROBAT:
		return "adobe_acrobat"
	case CHROME:
		return "chrome"
	case MICROSOFT_OFFICE:
		return "microsoft_office"
	case SEVEN_Z:
		return "seven_z"
	case NOTEPAD_PLUS_PLUS:
		return "notepad_plus_plus"
	case WINRAR:
		return "winrar"
	case VISUAL_STUDIO_CODE:
		return "visual_studio_code"
	case POSTMAN:
		return "postman"
	case DBEAVER:
		return "dbeaver"
	case ANYDESK:
		return "anydesk"
	case TEAMVIEWER:
		return "teamviewer"
	case ZOOM:
		return "zoom"
	case REALVNC_VIEWER:
		return "realvnc_viewer"
	case POSTGRESQL:
		return "postgresql"
	default:
		return "Unknown"
	}
}

func (app ThirdPartyApplication) ToThirdPartyApplication(application string) ThirdPartyApplication {
	switch application {
	case "firefox":
		return FIREFOX
	case "vlc":
		return VLC
	case "adobe_acrobat_reader_mui":
		return ADOBE_ACROBAT_READER_MUI
	case "adobe_acrobat_reader":
		return ADOBE_ACROBAT_READER
	case "adobe_acrobat":
		return ADOBE_ACROBAT
	case "chrome":
		return CHROME
	case "microsoft_office":
		return MICROSOFT_OFFICE
	case "seven_z":
		return SEVEN_Z
	case "notepad_plus_plus":
		return NOTEPAD_PLUS_PLUS
	case "winrar":
		return WINRAR
	case "visual_studio_code":
		return VISUAL_STUDIO_CODE
	case "postman":
		return POSTMAN
	case "dbeaver":
		return DBEAVER
	case "anydesk":
		return ANYDESK
	case "teamviewer":
		return TEAMVIEWER
	case "zoom":
		return ZOOM
	case "realvnc_viewer":
		return REALVNC_VIEWER
	case "postgresql":
		return POSTGRESQL
	default:
		return UNKNOWN_APP
	}
}

type UnixOsFlavour int

const (
	UBUNTU UnixOsFlavour = iota + 1
)

func (os UnixOsFlavour) String() string {
	switch os {
	case UBUNTU:
		return "ubuntu"
	default:
		return ""
	}
}
