package server

import (
	"github.com/gorilla/mux"
	"golang.org/x/net/http2"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/controller"
	"patch-central-repo/logger"
	"path/filepath"
	"time"
)

type HttpServer struct {
	httpServer *http.Server
}

func NewServer() *HttpServer {
	address := common.GetEnv("HTTP_SERVER_HOST", "0.0.0.0")
	port := common.GetEnv("HTTP_SERVER_PORT", "8080")
	handler := mux.NewRouter()
	controller.Handle(handler)
	httpServer := &http.Server{
		Addr:        address + ":" + port,
		Handler:     handler,
		IdleTimeout: 5 * time.Minute,
	}
	return &HttpServer{httpServer: httpServer}
}

func (server *HttpServer) ListenAndServe() error {
	err := http2.ConfigureServer(server.httpServer, &http2.Server{})
	if err != nil {
		panic(err)
	}
	go func() {
		workingDir := common.CurrentWorkingDir()
		certFile := filepath.Join(workingDir, "server.pem")
		keyFile := filepath.Join(workingDir, "server.key")
		err := server.httpServer.ListenAndServeTLS(certFile, keyFile)
		if err != nil {
			panic(err)
		}
		logger.ServiceLogger.Info("HttpServer now listening")
	}()
	return nil
}

func (server *HttpServer) Shutdown() error {
	logger.ServiceLogger.Info("Shutting down server")
	if server.httpServer != nil {
		err := server.httpServer.Close()
		server.httpServer = nil
		if err != nil {
			return err
		}
	}
	return nil
}
