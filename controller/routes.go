package controller

import (
	"encoding/json"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/handler"
	"patch-central-repo/logger"
	"patch-central-repo/middleware"
)

func Handle(route *mux.Router) {
	route.Use(middleware.AuthMiddleware, middleware.CommonMiddleware)

	//route.Methods(http.MethodGet).Path("/home").Handler(http.HandlerFunc(HomeHandler))
	//route.Methods(http.MethodGet).Path("/login/{user}/{pass}").Handler(http.HandlerFunc(LoginHandler))

	//To Use Middleware to specific api
	//
	//apiRoute.Methods(http.MethodGet).Path("/protected").Handler(AuthMiddleware(http.HandlerFunc(DashboardHandler)))
	//apiRoute.Methods(http.MethodGet).Path("/protected").HandlerFunc(DashboardHandler)

	apiRouter := route.PathPrefix("/api").Subrouter()

	applyDownloadAPIRoutes(apiRouter)
	//Windows Patch Api
	applyWindowsPatchRoutes(apiRouter)
	//Ubuntu Patch Api
	applyUbuntuPatchRoutes(apiRouter)
	// Mac Patch API
	applyMacPatchRoutes(apiRouter)
	// ThirdPartyPackage API
	applyThirdPartyRoutes(apiRouter)
	//User API
	//applyUserRoutes(apiRoute)
}

func applyDownloadAPIRoutes(route *mux.Router) {
	//File Download api
	route.Methods(http.MethodGet).Path("/xml/download/{filename}").HandlerFunc(handler.XmlDownloadFile)
	route.Methods(http.MethodGet).Path("/cab/download/{filename}").HandlerFunc(handler.CabDownloadFile)
	route.Methods(http.MethodGet).Path("/cab/download/dir/{filename}").HandlerFunc(handler.CabDownloadDirectory)
}

func applyWindowsPatchRoutes(route *mux.Router) {
	//Language Api
	languageHandler := handler.NewLanguageHandler()
	route.Methods(http.MethodPost).Path("/patch/language/search").HandlerFunc(languageHandler.GetAllLanguage)

	//Patch Category Api
	patchCategoryHandler := handler.NewPatchCategoryHandler()
	route.Methods(http.MethodPost).Path("/patch/category/search").HandlerFunc(patchCategoryHandler.GetAllCategory)

	//Patch Product Api
	patchProductHandler := handler.NewPatchProductHandler()
	route.Methods(http.MethodPost).Path("/patch/product/search").HandlerFunc(patchProductHandler.GetAllProduct)

	//Windows Patch Api
	windowsPatchHandler := handler.NewWindowsPatchHandler()
	route.Methods(http.MethodPost).Path("/patch/windows/search").HandlerFunc(windowsPatchHandler.GetAllWindowsPatch)
	route.Methods(http.MethodGet).Path("/patch/windows/get-all-category-xml-list").HandlerFunc(windowsPatchHandler.GetAllCategoryXmlList)
	route.Methods(http.MethodGet).Path("/patch/windows/prepare-xml-zip").HandlerFunc(windowsPatchHandler.PrepareXmlZip)

	cabSyncHistoryHandler := handler.NewCabSyncHistoryHandler()
	route.Methods(http.MethodPost).Path("/patch/cab/history/search").HandlerFunc(cabSyncHistoryHandler.GetAllWindowsCabHistory)

}

func applyUbuntuPatchRoutes(route *mux.Router) {
	ubuntuPatchHandler := handler.NewUbuntuPatchHandler()
	route.Methods(http.MethodPost).Path("/patch/ubuntu-patch/search").HandlerFunc(ubuntuPatchHandler.GetAllUbuntuPatch)
	route.Methods(http.MethodPost).Path("/patch/ubuntu-notice-data/search").HandlerFunc(ubuntuPatchHandler.GetAllNoticeData)
	route.Methods(http.MethodPost).Path("/patch/ubuntu-release-package/search").HandlerFunc(ubuntuPatchHandler.GetAllReleasePackage)
	route.Methods(http.MethodPost).Path("/patch/linux-package/search").HandlerFunc(ubuntuPatchHandler.GetAllLinuxPackages)
}

func applyMacPatchRoutes(route *mux.Router) {
	macPatchHandler := handler.NewMacPatchHandler()
	route.Methods(http.MethodPost).Path("/patch/mac-patch/search").HandlerFunc(macPatchHandler.GetAllMacPatch)
}

func applyThirdPartyRoutes(route *mux.Router) {
	thirdPartyHandler := handler.NewThirdPartyPackageHandler()
	route.Methods(http.MethodPost).Path("/patch/third-party/search").HandlerFunc(thirdPartyHandler.GetAllThirdPartyPackage)
	route.Methods(http.MethodPost).Path("/patch/third-party/create").HandlerFunc(thirdPartyHandler.CreateThirdPartyPackage)
	route.Methods(http.MethodPut).Path("/patch/third-party/update").HandlerFunc(thirdPartyHandler.UpdateThirdPartyPackage)
	route.Methods(http.MethodPost).Path("/patch/third-party/upload").HandlerFunc(thirdPartyHandler.UploadThirdPartyPackageXml)
}

func applyUserRoutes(route *mux.Router) {
	userHandler := handler.NewUserHandler()
	route.Methods(http.MethodGet).Path("/user/{id}").HandlerFunc(userHandler.GetUserHandler)
	route.Methods(http.MethodPost).Path("/user").HandlerFunc(userHandler.CreateUserHandler)
	route.Methods(http.MethodPut).Path("/user/{id}").HandlerFunc(userHandler.UpdateUserHandler)
	route.Methods(http.MethodDelete).Path("/user/{id}").HandlerFunc(userHandler.DeleteUserHandler)
	route.Methods(http.MethodPost).Path("/user/search").HandlerFunc(userHandler.GetAllUserHandler)
}

func LoginHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	user, _ := vars["user"]
	if user != "admin" {
		jsonData, _ := common.RestToJson(w, common.Error("Enter Valid username and password", http.StatusUnauthorized))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[LoginHandler]", err.Error())
			return
		}
		return
	}

	pass, _ := vars["pass"]
	if pass != "admin" {
		jsonData, _ := common.RestToJson(w, common.Error("Enter Valid username and password", http.StatusUnauthorized))
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[LoginHandler]", err.Error())
			return
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	generateJWT, _ := middleware.GenerateJWT()
	m := map[string]interface{}{
		"token": generateJWT,
	}
	jsonData, _ := json.Marshal(&m)
	_, err := fmt.Fprintf(w, string(jsonData))
	if err != nil {
		logger.ServiceLogger.Error("[LoginHandler]", err.Error())
		return
	}
	return
}
