<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<upd:Update xmlns:upd="http://schemas.microsoft.com/msus/2002/12/Update" xmlns:bar="http://schemas.microsoft.com/msus/2002/12/BaseApplicabilityRules" xmlns:lar="http://schemas.microsoft.com/msus/2002/12/LogicalApplicabilityRules" xmlns:mar="http://schemas.microsoft.com/msus/2002/12/MsiApplicabilityRules" xmlns:msp="http://schemas.microsoft.com/msus/2002/12/UpdateHandlers/WindowsInstaller" xmlns:pub="http://schemas.microsoft.com/msus/2002/12/Publishing">
	<upd:UpdateIdentity  UpdateID="{#uuid#}"/>
	<upd:Properties DefaultPropertiesLanguage="en"/>
	<upd:ZirozenProperties IsThirdPartyPatch="Yes"/>
	<upd:ApplicabilityRules>
		<upd:IsInstallable>
			<lar:And>
				<!-- 
                    Identying 
                    64-bit processor = 9
                    32-bit processor = 0 
                -->
				<bar:Processor Architecture="{#osArch#}"/>
				<bar:RegKeyExists Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#productCode#}"/> 
				<!-- DC Base version check -->
				<bar:RegSzToVersion Comparison="GreaterThanOrEqualTo" Data="15.007.20033" Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#productCode#}" Value="DisplayVersion"/> 
				<bar:RegSzToVersion Comparison="LessThan" Data="{#version#}" Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#productCode#}" Value="DisplayVersion"/> 
			</lar:And>
		</upd:IsInstallable>
		<upd:IsInstalled>
			<bar:RegSzToVersion Comparison="GreaterThanOrEqualTo" Data="15.007.20033" Key="HKEY_LOCAL_MACHINE" Subkey="SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{#productCode#}" Value="DisplayVersion"/> 
		</upd:IsInstalled>
	</upd:ApplicabilityRules>
</upd:Update>
