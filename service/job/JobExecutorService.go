package scheduler

import (
	"github.com/go-co-op/gocron/v2"
	"patch-central-repo/logger"
	"patch-central-repo/service/linux/ubuntu"
	"patch-central-repo/service/macos"
	"patch-central-repo/service/thirdparty"
	"patch-central-repo/service/windows"
	"runtime"
	"time"
)

type JobExecutorService struct {
}

func NewJobExecutorService() *JobExecutorService {
	return new(JobExecutorService)
}

func (service JobExecutorService) Init() {
	scheduler, err := gocron.NewScheduler()
	if err != nil {
		return
	}
	if runtime.GOOS == "windows" { // to import patch in wsus from update catalog
		_, err = scheduler.NewJob(gocron.DailyJob(1, gocron.NewAtTimes(gocron.NewAtTime(0, 0, 0))), gocron.NewTask(windows.NewWindowsPatchService().SyncWindowsPatchFromUpdateCatalog))
		if err != nil {
			logger.ServiceLogger.Info("failed to schedule windows patch sync from update catalog job")
			return
		}
		_, err = scheduler.NewJob(gocron.DailyJob(1, gocron.NewAtTimes(gocron.NewAtTime(0, 0, 0))), gocron.NewTask(windows.NewWindowsPatchService().SyncWindowsDotNetPatchFromUpdateCatalog))
		if err != nil {
			logger.ServiceLogger.Info("failed to schedule windows dot net patch sync from update catalog job")
			return
		}
	} else {
		_, err = scheduler.NewJob(gocron.DurationJob(6*time.Hour), gocron.NewTask(macos.NewMacOsPatchService().SyncMacOsPatches))
		if err != nil {
			logger.ServiceLogger.Info("failed to schedule macos patch sync job")
			return
		}
		_, err = scheduler.NewJob(gocron.DurationJob(6*time.Hour), gocron.NewTask(ubuntu.NewUbuntuNoticeDataService().SyncUbuntuNoticeData))
		if err != nil {
			logger.ServiceLogger.Info("failed to schedule ubuntu notice data sync job")
			return
		}
		_, err = scheduler.NewJob(gocron.DailyJob(1, gocron.NewAtTimes(gocron.NewAtTime(0, 0, 0))), gocron.NewTask(thirdparty.NewThirdPartyPackageService().SyncThirdPartyPatchRepo))
		if err != nil {
			logger.ServiceLogger.Info("failed to schedule third party patch sync job")
			return
		}
		_, err = scheduler.NewJob(gocron.DailyJob(1, gocron.NewAtTimes(gocron.NewAtTime(0, 0, 0))), gocron.NewTask(windows.NewWindowsPatchService().SyncWindowsPatches))
		if err != nil {
			logger.ServiceLogger.Info("failed to schedule windows patch sync job")
			return
		}
		_, err = scheduler.NewJob(gocron.DailyJob(1, gocron.NewAtTimes(gocron.NewAtTime(0, 0, 0))), gocron.NewTask(ubuntu.NewUbuntuPatchService().SyncUbuntuMirrorPkg))
		if err != nil {
			logger.ServiceLogger.Info("failed to schedule ubuntu mirror package sync job")
			return
		}
	}

	scheduler.Start()
}
