package service

import (
	"patch-central-repo/common"
	"patch-central-repo/model/windows"
	"patch-central-repo/repository"
	"patch-central-repo/rest"
	"strings"
	"time"
)

type PatchProductService struct {
	Repository *repository.PatchProductRepository
}

func NewPatchProductService() *PatchProductService {
	return &PatchProductService{
		Repository: repository.NewPatchProductRepository(),
	}
}

func (service PatchProductService) CreateOrGetProduct(name, uuid, subCategoryUUID, subCategory string) int64 {
	pchProduct, _ := service.Repository.FindByUUID(uuid)
	if pchProduct.Id == 0 {
		pchProduct = windows.PatchProduct{}
		pchProduct.UUID = uuid
		pchProduct.CreatedTime = time.Now().UnixMilli()
		pchProduct.UpdatedTime = time.Now().UnixMilli()
		pchProduct.OriginalText = name
		pchProduct.SubCategory = subCategory
		pchProduct.SubCategoryUUID = subCategoryUUID

		productName := name
		servicePack := ""
		if strings.Contains("RTM", productName) {
			servicePack = "RTM"
			productName = strings.ReplaceAll(productName, "RTM", "")
		}
		productName = strings.ReplaceAll(productName, "Detectoid for", "")
		productName = strings.ReplaceAll(productName, "Detectoid", "")
		productName = strings.ReplaceAll(productName, "Detectoid:", "")

		secondaryServicePack := ""
		if strings.Contains(productName, "SP") {
			servicePack = "SP"
		} else if strings.Contains(productName, "UbuntuPatchService Pack") {
			servicePack = "UbuntuPatchService Pack"
		}

		if servicePack == "" {
			servicePack = secondaryServicePack
		} else {
			if secondaryServicePack != "" {
				servicePack = servicePack + "," + secondaryServicePack
			}
		}

		if strings.HasPrefix(productName, "Win10") {
			tempProductName := "Windows "
			version := ""
			versionDetails := strings.SplitN(productName, "Version", 1)
			if len(versionDetails) > 1 {
				version = versionDetails[1]
				if strings.Contains(productName, "Server") {
					version = strings.ReplaceAll(version, "Server", "")
				}
			}
			version = strings.ReplaceAll(version, "RTM", "")
			if strings.Contains(productName, "Server") {
				tempProductName = tempProductName + "Server Version " + version
			} else {
				tempProductName = tempProductName + "10 Version " + version

			}
			productName = tempProductName
		}
		pchProduct.Name = strings.Trim(productName, " ")
		pchProduct.ServicePack = strings.Trim(servicePack, " ")
		productId, _ := service.Repository.Create(&pchProduct)
		return productId
	}
	return pchProduct.Id
}

func (service PatchProductService) GetAllProduct(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.PatchProduct.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []windows.PatchProduct
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.PatchProduct.String(), false)
		packagePageList, err = service.Repository.GetAllProducts(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
