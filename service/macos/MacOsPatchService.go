package macos

import (
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"howett.net/plist"
	"html"
	"log"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/macos"
	macosrepo "patch-central-repo/repository/macos"
	"patch-central-repo/rest"
	"path/filepath"
	"strings"
	"time"
)

type MacOsPatchService struct {
	Repository *macosrepo.MacOsPatchRepository
}

var (
	SWSCAN_URL         = "https://swscan.apple.com/content/catalogs/others/index-%s.merged-1.sucatalog"
	SUPPORTED_VERSIONS = []string{"10.15", "10.16", "12", "13", "14", "15"}
)

func NewMacOsPatchService() *MacOsPatchService {
	return &MacOsPatchService{
		Repository: macosrepo.NewMacOsPatchRepository(),
	}
}

func (service MacOsPatchService) SyncMacOsPatches() {
	logger.ServiceLogger.Info("starting macos package sync...")
	for _, version := range SUPPORTED_VERSIONS {
		url := fmt.Sprintf(SWSCAN_URL, version)
		fileName := common.FilenameFromUrl(url)
		dataDirectory := filepath.Join(common.MacDataDirectoryPath(), version, "catalog")
		_, err := os.Stat(dataDirectory)
		if os.IsNotExist(err) {
			err := os.MkdirAll(dataDirectory, 0755)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}
		logger.ServiceLogger.Info("[SyncMacOsPatches] Downloading macos catalog for " + version)
		success := common.DownloadFileAndCopyToFilePath(url, dataDirectory, fileName)
		if success {
			logger.ServiceLogger.Info("[SyncMacOsPatches] processing macos catalog file for " + version)
			processMacCatalogData(dataDirectory, fileName, version)
			logger.ServiceLogger.Info("[SyncMacOsPatches] macos catalog processing completed for " + version)
		} else {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while downloading mac catalog filepath : %s", filepath.Join(dataDirectory, fileName)))
		}
	}
	createZipFile()
}

func processMacCatalogData(dataDirectory string, fileName string, version string) {
	catalogData, err := os.ReadFile(filepath.Join(dataDirectory, fileName))
	if err != nil || catalogData == nil {
		log.Fatal(err)
		return
	}
	var plistData map[string]interface{}
	_, err = plist.Unmarshal(catalogData, &plistData)
	if err != nil {
		log.Fatal(err)
	}
	if plistData["Products"] != nil {
		repository := macosrepo.NewMacOsPatchRepository()
		if products, ok := plistData["Products"].(map[string]interface{}); ok {
			for productKey := range products {
				osVersions := make(map[string]struct{})
				macOsPatch, _ := repository.GetByProductKey(productKey)
				if macOsPatch.Id == 0 {
					macOsPatch.CreatedTime = time.Now().UnixMilli()
				} else {
					keys := strings.Split(macOsPatch.OsVersion, ",")
					for _, key := range keys {
						osVersions[key] = struct{}{}
					}
				}
				osVersions[version] = struct{}{}
				macOsPatch.UpdatedTime = time.Now().UnixMilli()
				keys := make([]string, 0, len(osVersions))
				for key := range osVersions {
					keys = append(keys, key)
				}
				macOsPatch.ProductKey = productKey
				macOsPatch.OsVersion = strings.Join(keys, ",")
				product, _ := products[productKey].(map[string]interface{})
				macOsPatch = processProductData(macOsPatch, product, dataDirectory, version)
				if macOsPatch.ProductType != "" {
					macOsPatch = processDistribution(macOsPatch, product, dataDirectory, version)
					if macOsPatch.Id == 0 {
						_, err = repository.Create(&macOsPatch)
					} else {
						_, err = repository.Update(&macOsPatch)
					}
				}
			}
		} else {
			log.Fatal("Products key not found or is not a map")
		}
	}
}

func processProductData(macOsPatch macos.MacOsPatch, product map[string]interface{}, dataDirectory string, osVersion string) macos.MacOsPatch {
	productValue := product["PostDate"]
	if productValue != nil {
		macOsPatch.ReleaseDate = productValue.(time.Time).UnixMilli()
	}
	productValue = product["ServerMetadataURL"]
	if productValue != nil {
		macOsPatch = processSMDUrl(macOsPatch, productValue.(string), dataDirectory, osVersion)
	}
	productValue = product["Packages"]
	if productValue != nil {
		macOsPatch = processPackages(macOsPatch, productValue.([]interface{}))
	}
	productValue = product["ExtendedMetaInfo"]
	if productValue != nil {
		macOsPatch = processProductType(macOsPatch, productValue.(map[string]interface{}), dataDirectory, osVersion)
	}
	return macOsPatch
}

func processDistribution(macOsPatch macos.MacOsPatch, product map[string]interface{}, dataDirectory string, osVersion string) macos.MacOsPatch {
	distributions := product["Distributions"]
	if distributions != nil {
		distributionsMap := distributions.(map[string]interface{})
		if distributionsMap != nil && distributionsMap["English"] != nil {
			url := distributionsMap["English"].(string)
			macOsPatch.DistributionFileName = common.FilenameFromUrl(url)
			dataDirectory = filepath.Join(common.MacDataDirectoryPath(), osVersion, "dist")
			_, err := os.Stat(dataDirectory)
			if os.IsNotExist(err) {
				err := os.MkdirAll(dataDirectory, 0755)
				if err != nil {
					logger.ServiceLogger.Error(err)
				}
			}
			success := common.DownloadFileAndCopyToFilePath(url, dataDirectory, macOsPatch.DistributionFileName)
			if !success {
				logger.ServiceLogger.Error(fmt.Sprintf("Error while downloading mac dist filepath : %s, productKey : %s", filepath.Join(dataDirectory, macOsPatch.DistributionFileName), macOsPatch.ProductKey))
			}
		}
	}
	return macOsPatch
}

func processSMDUrl(macOsPatch macos.MacOsPatch, smdUrl string, dataDirectory string, osVersion string) macos.MacOsPatch {
	fileName := common.FilenameFromUrl(smdUrl)
	dataDirectory = filepath.Join(common.MacDataDirectoryPath(), osVersion, "smd")
	_, err := os.Stat(dataDirectory)
	if os.IsNotExist(err) {
		err := os.MkdirAll(dataDirectory, 0755)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	success := common.DownloadFileAndCopyToFilePath(smdUrl, dataDirectory, fileName)
	if success {
		smdResponse, err := os.ReadFile(filepath.Join(dataDirectory, fileName))
		if err != nil || smdResponse == nil {
			log.Fatal(err)
		}
		var plistData map[string]interface{}
		_, err = plist.Unmarshal(smdResponse, &plistData)
		if err != nil {
			log.Fatal(err)
		}
		if plistData["localization"] != nil {
			localizations := plistData["localization"].(map[string]interface{})
			if localizations != nil && localizations["English"] != nil {
				localization := localizations["English"].(map[string]interface{})
				if localization != nil {
					if localization["title"] != nil {
						macOsPatch.Name = localization["title"].(string)
					}
					if localization["description"] != nil {
						macOsPatch.Description = decodeDescription(string(localization["description"].([]uint8)))
					}
				}
			}
		}
		if plistData["CFBundleShortVersionString"] != nil {
			macOsPatch.Version = plistData["CFBundleShortVersionString"].(string)
		}
	} else {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while downloading mac smd filepath : %s", filepath.Join(dataDirectory, fileName)))
	}
	return macOsPatch
}

func processPackages(macOsPatch macos.MacOsPatch, packages []interface{}) macos.MacOsPatch {
	if packages != nil && len(packages) > 0 {
		packageMapSet := make(map[string]struct{}) // To track unique package URLs
		for _, macPackage := range macOsPatch.Packages {
			if macPackage.FileName != "" {
				packageMapSet[macPackage.FileName] = struct{}{}
			}
		}
		for _, packageInfo := range packages {
			packageMap := packageInfo.(map[string]interface{})
			if packageMap["Size"] != nil || packageMap["URL"] != nil {
				url := packageMap["URL"].(string)
				fileName := common.FilenameFromUrl(url)
				if _, exists := packageMapSet[fileName]; exists {
					continue
				}
				macPackage := model.FileData{
					Size:        int64(packageMap["Size"].(uint64)),
					DownloadUrl: packageMap["URL"].(string),
					FileName:    fileName,
				}
				macOsPatch.Packages = append(macOsPatch.Packages, macPackage)
			}
		}
	}
	return macOsPatch
}

func processProductType(macOsPatch macos.MacOsPatch, extendedMetaInfo map[string]interface{}, dataDirectory string, osVersion string) macos.MacOsPatch {
	if extendedMetaInfo != nil && len(extendedMetaInfo) > 0 {
		metaInfo := extendedMetaInfo["ProductType"]
		if metaInfo != nil {
			macOsPatch.ProductType = metaInfo.(string)
		}
		metaInfo = extendedMetaInfo["InstallAssistantPackageIdentifiers"]
		if metaInfo != nil {
			installAssistantPackageIdentifiers := metaInfo.(map[string]interface{})
			for packageIdentifierKey, packageIdentifierValue := range installAssistantPackageIdentifiers {
				if packageIdentifierKey == "SharedSupport" {
					macOsPatch.ProductType = "macOsVersionUpdate"
					macOsPatch.Description = "Upgrade to " + packageIdentifierValue.(string)[strings.LastIndex(packageIdentifierValue.(string), ".")+1:]

					for _, macPackage := range macOsPatch.Packages {
						if macPackage.FileName == "BuildManifest.plist" {
							dataDirectory = filepath.Join(common.MacDataDirectoryPath(), osVersion, "BuildManifest")
							_, err := os.Stat(dataDirectory)
							if os.IsNotExist(err) {
								err := os.MkdirAll(dataDirectory, 0755)
								if err != nil {
									logger.ServiceLogger.Error(err)
								}
							}
							success := common.DownloadFileAndCopyToFilePath(macPackage.DownloadUrl, dataDirectory, macPackage.FileName)
							if success {
								smdResponse, err := os.ReadFile(filepath.Join(dataDirectory, macPackage.FileName))
								if err != nil || smdResponse == nil {
									log.Fatal(err)
								}
								var plistData map[string]interface{}
								_, err = plist.Unmarshal(smdResponse, &plistData)
								if err != nil {
									log.Fatal(err)
								}
								if plistData["ProductVersion"] != nil {
									macOsPatch.Name = "macOS " + plistData["ProductVersion"].(string)
									macOsPatch.Version = plistData["ProductVersion"].(string)
								}
							} else {
								logger.ServiceLogger.Error(fmt.Sprintf("Error while downloading mac BuildManifest filepath : %s", filepath.Join(dataDirectory, macPackage.FileName)))
							}
						}
					}
				}
			}
		}
	}
	return macOsPatch
}

func decodeDescription(description string) string {
	var stringBuilder strings.Builder
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html.UnescapeString(description)))
	if err != nil {
		log.Fatal(err)
	}
	// Find and print the text content of each <p> tag
	doc.Find("p").Each(func(index int, element *goquery.Selection) {
		text := element.Text()
		stringBuilder.WriteString(text)
		stringBuilder.WriteString("\n")
	})

	return strings.TrimSpace(stringBuilder.String())
}

func createZipFile() {
	macDir := common.MacDataDirectoryPath()
	for _, version := range SUPPORTED_VERSIONS {
		zipFile := filepath.Join(macDir, strings.ReplaceAll(version, " ", "")+".7z")
		distDirectory := filepath.Join(macDir, version, "dist")
		_, err := os.Stat(zipFile)
		if !os.IsNotExist(err) {
			err := os.Remove(zipFile)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}
		if _, err := os.Stat(distDirectory); os.IsNotExist(err) {
			logger.ServiceLogger.Fatal(fmt.Sprintf("Error while creating zip file for version : %s", version))
		} else {
			common.CreateZipFile(zipFile, distDirectory)
		}
	}
	//.7z will store in xml data folder
	macZipFile := filepath.Join(common.XMLDirectoryPath(), "mac.7z")
	files := filepath.Join(macDir, "*.7z")
	_, err := os.Stat(macZipFile)
	if !os.IsNotExist(err) {
		err := os.Remove(macZipFile)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	common.CreateZipFile(macZipFile, files)

}

func (service MacOsPatchService) GetAllMacPatch(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.MacOsPatch.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []macos.MacOsPatch
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.MacOsPatch.String(), false)
		packagePageList, err = service.Repository.GetAllMacPatch(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
