package service

import (
	"patch-central-repo/common"
	"patch-central-repo/model"
	"patch-central-repo/rest"
)

func PerformPartialUpdateForBase(entityModel *model.BaseEntityModel, entityRest rest.BaseEntityRest) map[string]map[string]interface{} {
	diffMap := make(map[string]map[string]interface{})

	if entityRest.PatchMap["name"] != nil && entityModel.Name != entityRest.Name {
		diffMap = common.AddInDiffMap("name", entityModel.Name, entityRest.Name)
		entityModel.Name = entityRest.Name
	}

	return diffMap
}

func ConvertToBaseEntityModel(rest rest.BaseEntityRest) model.BaseEntityModel {
	return model.BaseEntityModel{
		Id:          rest.Id,
		Name:        rest.Name,
		CreatedTime: rest.CreatedTime,
		UpdatedTime: rest.UpdatedTime,
	}
}

func ConvertToBaseEntityRest(entityModel model.BaseEntityModel) rest.BaseEntityRest {
	return rest.BaseEntityRest{
		Id:          entityModel.Id,
		Name:        entityModel.Name,
		CreatedTime: entityModel.CreatedTime,
		UpdatedTime: entityModel.UpdatedTime,
	}
}
