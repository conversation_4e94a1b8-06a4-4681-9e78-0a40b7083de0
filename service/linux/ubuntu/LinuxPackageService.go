package ubuntu

import (
	"patch-central-repo/common"
	"patch-central-repo/model/linux/ubuntu"
	"patch-central-repo/repository/linux/ubutnu"
	"patch-central-repo/rest"
)

type LinuxPackageService struct {
	Repository *ubutnu.LinuxPackageRepository
}

func NewLinuxPackageService() *LinuxPackageService {
	return &LinuxPackageService{
		Repository: ubutnu.NewLinuxPackageRepository(),
	}
}

func (service LinuxPackageService) GetAllLinuxPackage(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.LinuxPackages.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []ubuntu.LinuxPackage
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.LinuxPackages.String(), false)
		packagePageList, err = service.Repository.GetAllLinuxPackages(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
