package ubuntu

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/ubuntu"
	"patch-central-repo/repository/linux/ubutnu"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type UbuntuPatchService struct {
	Repository *ubutnu.UbuntuPatchRepository
}

var (
	UBUNTU_ARCHIVE_URL                        = "http://archive.ubuntu.com/ubuntu/dists/%s/%s/%s/Packages.gz"
	SUPPORTED_UBUNTU_VERSIONS                 = []string{"bionic", "devel", "focal", "jammy", "noble", "oracular", "plucky", "trusty", "xenial"}
	SUPPORTED_UBUNTU_CHANNEL                  = []string{"", "updates", "security"}
	SUPPORTED_UBUNTU_CHANNEL_SUPPORT_CATEGORY = []string{"main", "multiverse", "restricted", "universe"}
	SUPPORTED_UBUNTU_SUPPORTED_ARCH           = []string{"binary-amd64"}
)

func NewUbuntuPatchService() *UbuntuPatchService {
	return &UbuntuPatchService{
		Repository: ubutnu.NewUbuntuPatchRepository(),
	}
}

func (service UbuntuPatchService) convertToRest(patchInfo ubuntu.UbuntuPatch) rest.UbuntuPatchRest {
	return rest.UbuntuPatchRest{
		BaseEntityRest:    service2.ConvertToBaseEntityRest(patchInfo.BaseEntityModel),
		UUID:              patchInfo.UUID,
		OsVersion:         patchInfo.OsVersion,
		Channel:           patchInfo.Channel,
		Repo:              patchInfo.Repo,
		PackageName:       patchInfo.PackageName,
		Arch:              patchInfo.Arch,
		Version:           patchInfo.Version,
		Priority:          patchInfo.Priority,
		Section:           patchInfo.Section,
		Origin:            patchInfo.Origin,
		Depends:           patchInfo.Depends,
		Breaks:            patchInfo.Breaks,
		FileName:          patchInfo.FileName,
		DownloadUrl:       patchInfo.DownloadUrl,
		Size:              patchInfo.Size,
		Sha1:              patchInfo.Sha1,
		PkgAndVersion:     patchInfo.PkgAndVersion,
		PkgNameWithDistro: patchInfo.PkgNameWithDistro,
		Downloadable:      patchInfo.Downloadable,
		ReleaseDate:       patchInfo.ReleaseDate,
	}
}

func (service UbuntuPatchService) SyncUbuntuMirrorPkg() {
	logger.ServiceLogger.Info("Process started to Sync Ubuntu Mirror Pkg")
	for _, version := range SUPPORTED_UBUNTU_VERSIONS {
		for _, repo := range SUPPORTED_UBUNTU_CHANNEL {
			channelAlice := version
			if repo != "" {
				channelAlice += "-" + repo
			}
			for _, channel := range SUPPORTED_UBUNTU_CHANNEL_SUPPORT_CATEGORY {
				time.Sleep(5 * time.Second)
				for _, arch := range SUPPORTED_UBUNTU_SUPPORTED_ARCH {
					pkgUrl := fmt.Sprintf(UBUNTU_ARCHIVE_URL, channelAlice, channel, arch)
					pkgFileName := strings.Join([]string{version, repo, channel, arch}, "_")
					pkgFilePath := filepath.Join(common.LinuxDataDirectoryPath(), common.UBUNTU.String(), version)
					logger.ServiceLogger.Info("[SyncUbuntuMirrorPkg] Downloading ubuntu patch data for " + pkgFileName)
					success := common.DownloadFileAndCopyToFilePath(pkgUrl, pkgFilePath, pkgFileName+".gz")
					if success {
						logger.ServiceLogger.Info("[SyncUbuntuMirrorPkg] DecompressGzipFile ubuntu patch data for " + pkgFileName)
						err := common.DecompressGzipFile(filepath.Join(pkgFilePath, pkgFileName+".gz"), filepath.Join(pkgFilePath, pkgFileName))
						if err != nil {
							logger.ServiceLogger.Error(fmt.Sprintf("[SyncUbuntuMirrorPkg] Error while decompress Gzip for filepath : %s", filepath.Join(pkgFilePath, pkgFileName+".gz")))
						} else {
							logger.ServiceLogger.Info("[SyncUbuntuMirrorPkg] Process ubuntu patch data : " + pkgFileName)
							go processUbuntuData(version, repo, channel, arch, filepath.Join(pkgFilePath, pkgFileName))
							logger.ServiceLogger.Info("[SyncUbuntuMirrorPkg] Processing completed for ubuntu patch data : " + pkgFileName)
						}
					} else {
						logger.ServiceLogger.Error(fmt.Sprintf("Error while downloading ubutnu meta patch filepath : %s", filepath.Join(pkgFilePath, pkgFileName+".gz")))
					}
					err := os.Remove(filepath.Join(pkgFilePath, pkgFileName+".gz"))
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
				}
			}
		}
	}
	logger.ServiceLogger.Info("Process completed to Sync Ubuntu Mirror Pkg")
}

func processUbuntuData(version, repo, channel, arch, pkgPath string) {
	pkdData, err := os.ReadFile(pkgPath)
	if err != nil || pkdData == nil {
		return
	}

	pkgList := strings.Split(string(pkdData), "\n\n")
	ubuntuPchRepo := ubutnu.NewUbuntuPatchRepository()
	if len(pkgList) > 0 {
		for _, pkg := range pkgList {
			fieldDetails := strings.Split(pkg, "\n")
			if len(fieldDetails) > 0 {
				var ubuntuPatch ubuntu.UbuntuPatch
				pkgDetailMap := map[string]interface{}{}
				for _, fieldDetail := range fieldDetails {
					if strings.Contains(fieldDetail, ":") {
						field := strings.Split(fieldDetail, ":")[0]
						value := strings.Split(fieldDetail, ":")[1]
						pkgDetailMap[strings.TrimSpace(field)] = strings.TrimSpace(value)
					}
				}
				if len(pkgDetailMap) > 0 {
					pkgDetailMapData, _ := json.Marshal(pkgDetailMap)
					if len(pkgDetailMapData) > 0 {
						err := json.Unmarshal(pkgDetailMapData, &ubuntuPatch)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						if ubuntuPatch.PackageName != "" {
							if val, ok := pkgDetailMap["Size"]; ok && val != nil {
								size, _ := strconv.ParseInt(val.(string), 10, 64)
								ubuntuPatch.Size = size
							}
							ubuntuPatch.PkgAndVersion = ubuntuPatch.PackageName + "@_@" + ubuntuPatch.Version
							ubuntuPatch.Channel = channel
							ubuntuPatch.OsVersion = version
							ubuntuPatch.Repo = repo
							ubuntuPatch.UpdatedTime = time.Now().UnixMilli()
							ubuntuPatch.UUID = version + repo + channel + arch + ubuntuPatch.PkgAndVersion

							if repo == "" {
								ubuntuPatch.PkgNameWithDistro = ubuntuPatch.PackageName + "/" + version
							} else {
								ubuntuPatch.PkgNameWithDistro = ubuntuPatch.PackageName + "/" + version + "-" + repo
							}

							existingPkg, _ := ubuntuPchRepo.FindByUUID(ubuntuPatch.UUID)
							if existingPkg.Id != 0 {
								continue
							}
							downloadUrl := "http://in.archive.ubuntu.com/ubuntu/" + ubuntuPatch.FileName
							fileReleaseDate := getFileCreatedTimeFromUrl(downloadUrl)
							ubuntuPatch.ReleaseDate = fileReleaseDate
							ubuntuPatch.DownloadUrl = downloadUrl
							ubuntuPatch.Downloadable = true
							ubuntuPatch.CreatedTime = time.Now().UnixMilli()
							_, err := ubuntuPchRepo.Create(&ubuntuPatch)
							if err != nil {
								logger.ServiceLogger.Error(err)
							}
						}
					}
				}
			}
		}
	}
}

func getFileCreatedTimeFromUrl(fullUrl string) int64 {

	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport, Timeout: time.Second * 10}

	req, err := http.NewRequest("HEAD", fullUrl, nil)
	if err != nil {
		logger.ServiceLogger.Error("Error creating HTTP request ", err)
		return 0
	}

	resp, err := client.Do(req)
	if err != nil {
		logger.ServiceLogger.Error("Error making HTTP request ", err)
		return 0
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		logger.ServiceLogger.Error("HTTP request failed with status code ", resp.StatusCode)
		return 0
	}

	lastModifiedHeader := resp.Header.Get("Last-Modified")
	lastModifiedTime, err := time.Parse(time.RFC1123, lastModifiedHeader)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing Last-Modified header:", err)
		return 0
	}

	return lastModifiedTime.Unix()
}

func (service UbuntuPatchService) GetAllUbuntuPatch(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.UbuntuPatch.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []ubuntu.UbuntuPatch
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.UbuntuPatch.String(), false)
		packagePageList, err = service.Repository.GetAllUbuntuPatches(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
