package ubuntu

import (
	"patch-central-repo/common"
	"patch-central-repo/model/linux/ubuntu"
	"patch-central-repo/repository/linux/ubutnu"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
)

type UbuntuReleasePackageService struct {
	Repository *ubutnu.UbuntuReleasePackageRepository
}

func NewUbuntuReleasePackageService() *UbuntuReleasePackageService {
	return &UbuntuReleasePackageService{Repository: ubutnu.NewUbuntuReleasePackageRepository()}
}

func (service UbuntuReleasePackageService) ConvertToRest(pkg ubuntu.UbuntuReleasePackage) rest.UbuntuReleasePackageRest {
	return rest.UbuntuReleasePackageRest{
		BaseEntityRest: service2.ConvertToBaseEntityRest(pkg.BaseEntityModel),
		NoticeID:       pkg.NoticeID,
		OSVersion:      pkg.OSVersion,
		Description:    pkg.Description,
		IsSource:       pkg.IsSource,
		IsVisible:      pkg.IsVisible,
		Pocket:         pkg.Pocket,
		SourceLink:     pkg.SourceLink,
		Version:        pkg.Version,
		VersionLink:    pkg.VersionLink,
		NameAndVersion: pkg.NameAndVersion,
	}
}

func (service UbuntuReleasePackageService) convertListToRest(packages []ubuntu.UbuntuReleasePackage) []rest.UbuntuReleasePackageRest {
	var packageRestList []rest.UbuntuReleasePackageRest
	if len(packages) != 0 {
		for _, pkg := range packages {
			packageRest := service.ConvertToRest(pkg)
			packageRestList = append(packageRestList, packageRest)
		}
	}
	return packageRestList
}

func (service UbuntuReleasePackageService) GetAllUbuntuReleasePackage(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.UbuntuReleasePackage.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []ubuntu.UbuntuReleasePackage
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.UbuntuReleasePackage.String(), false)
		packagePageList, err = service.Repository.GetAllUbuntuReleasePackages(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(packagePageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
