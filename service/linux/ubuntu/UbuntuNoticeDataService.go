package ubuntu

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/linux/ubuntu"
	"patch-central-repo/repository/linux/ubutnu"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
	"strconv"
	"strings"
	"time"
)

type UbuntuNoticeDataService struct {
	Repository *ubutnu.UbuntuNoticeDataRepository
}

const (
	NoticeUrl = "https://ubuntu.com/security/notices/"
)

func NewUbuntuNoticeDataService() *UbuntuNoticeDataService {
	return &UbuntuNoticeDataService{Repository: ubutnu.NewUbuntuNoticeDataRepository()}
}

func (service UbuntuNoticeDataService) convertToRest(data ubuntu.UbuntuNoticeData) rest.UbuntuNoticeDataRest {
	releasePackagesRest := make(map[string][]rest.UbuntuReleasePackageRest, len(data.ReleasePackages))
	for key, packages := range data.ReleasePackages {
		var packagesRest []rest.UbuntuReleasePackageRest
		for _, pkg := range packages {
			packagesRest = append(packagesRest, NewUbuntuReleasePackageService().ConvertToRest(pkg))
		}
		releasePackagesRest[key] = packagesRest
	}

	return rest.UbuntuNoticeDataRest{
		BaseEntityRest:  service2.ConvertToBaseEntityRest(data.BaseEntityModel),
		CVEsIDs:         data.CVEsIDs,
		NoticeId:        data.NoticeId,
		Description:     data.Description,
		Instructions:    data.Instructions,
		IsHidden:        data.IsHidden,
		Published:       data.Published,
		Summary:         data.Summary,
		Title:           data.Title,
		Type:            data.Type,
		AffectedOS:      data.AffectedOS,
		SupportURL:      data.SupportURL,
		ReleaseDate:     data.ReleaseDate,
		ReleasePackages: releasePackagesRest,
	}
}

func (service UbuntuNoticeDataService) convertListToRest(packages []ubuntu.UbuntuNoticeData) []rest.UbuntuNoticeDataRest {
	var packageRestList []rest.UbuntuNoticeDataRest
	if len(packages) != 0 {
		for _, pkg := range packages {
			packageRest := service.convertToRest(pkg)
			packageRestList = append(packageRestList, packageRest)
		}
	}
	return packageRestList
}

func (service UbuntuNoticeDataService) SyncUbuntuNoticeData() {
	logger.ServiceLogger.Info("Process started to Sync Ubuntu Notice Data")
	noticeHistoryRepository := ubutnu.NewUbuntuNoticeHistoryRepository()
	noticeHistory, _ := noticeHistoryRepository.FindFirstByOrderByTotalSizeDescCreatedTimeDesc()

	offset := 0
	size := 20
	totalCount := 0

	if noticeHistory.Id != 0 {
		offset = noticeHistory.NoticeOffset + noticeHistory.TotalSize
		totalCount = noticeHistory.TotalSize * int(noticeHistory.Id)
	}
	retry := 0
	for offset <= size+totalCount {
		noticeResponse, err := getChunkWiseNoticeData(offset, size)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while getting notice data for offset : %v,size : %v , Error : %v", offset, size, err.Error()))
			if offset >= size+totalCount {
				break
			}
			time.Sleep(20 * time.Second)
			if retry < 100 {
				retry++
				continue
			}
		}
		retry = 0
		if noticeResponse != nil {
			totalCount = noticeResponse.TotalResults
			if noticeResponse.Notices != nil && len(noticeResponse.Notices) > 0 {
				for _, notice := range noticeResponse.Notices {
					processNoticeData(&notice)
				}
			}
			createNoticeHistory := ubuntu.UbuntuNoticeHistory{
				BaseEntityModel: model.BaseEntityModel{
					CreatedTime: time.Now().UnixMilli(),
				},
				NoticeOffset: offset,
				NoticeSize:   size,
				TotalSize:    len(noticeResponse.Notices),
			}
			_, err = noticeHistoryRepository.Create(&createNoticeHistory)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
			offset = offset + size
		}
	}

	logger.ServiceLogger.Info("Process completed to Sync Ubuntu Notice Data")
}

func processNoticeData(notice *ubuntu.UbuntuNoticeData) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while Processing Notice data : ", notice.NoticeId)
		}
	}()
	if notice.Published != "" {
		timeStr := "2004-10-23T01:17:50"
		releaseTime, err := time.Parse(time.RFC3339, timeStr)
		if err == nil {
			notice.ReleaseDate = releaseTime.UnixMilli()
		}
	}

	affectedProduct := ""
	for os := range notice.ReleasePackages {
		affectedProduct += os + ","
	}
	if strings.HasSuffix(affectedProduct, ",") {
		affectedProduct = affectedProduct[:len(affectedProduct)-1]
	}
	notice.AffectedOS = affectedProduct

	notice.SupportURL = NoticeUrl + notice.NoticeId
	notice.UpdatedTime = time.Now().UnixMilli()
	notice.CreatedTime = time.Now().UnixMilli()
	_, err := ubutnu.NewUbuntuNoticeDataRepository().Create(notice)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}

	releasePackageByOs := notice.ReleasePackages

	linuxPkgRepo := ubutnu.NewLinuxPackageRepository()
	releasePkgRepo := ubutnu.NewUbuntuReleasePackageRepository()
	if affectedProduct != "" {
		for _, os := range strings.Split(affectedProduct, ",") {
			releasePkgList := releasePackageByOs[os]
			if releasePkgList != nil && len(releasePkgList) > 0 {
				for _, releasePkg := range releasePkgList {
					releasePkg.NoticeID = notice.NoticeId
					releasePkg.OSVersion = os
					releasePkg.NameAndVersion = releasePkg.Name + "@_@" + releasePkg.Version + "@_@" + releasePkg.NoticeID + "@_@" + releasePkg.OSVersion
					releasePkgs, err := releasePkgRepo.FindByNameAndVersion(releasePkg.NameAndVersion)
					if err == nil && releasePkgs != nil && len(releasePkgs) > 0 {
						releasePkg.Id = releasePkgs[0].Id
						releasePkg.UpdatedTime = time.Now().UnixMilli()
						_, err := releasePkgRepo.Update(&releasePkg)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
					} else {
						releasePkg.CreatedTime = time.Now().UnixMilli()
						releasePkg.UpdatedTime = time.Now().UnixMilli()
						_, err := releasePkgRepo.Create(&releasePkg)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
					}

					linuxPkgList, err := linuxPkgRepo.FindByNameAndDistributionAndArch(releasePkg.Name, releasePkg.Version, os, common.NoArch.String())
					var linuxPkg ubuntu.LinuxPackage
					isLinuxPkgExist := false
					if err == nil && linuxPkgList != nil && len(linuxPkgList) > 0 {
						linuxPkg = linuxPkgList[0]
						linuxPkg.UpdatedTime = time.Now().UnixMilli()
						isLinuxPkgExist = true
					} else {
						linuxPkg = ubuntu.LinuxPackage{}
						linuxPkg.CreatedTime = time.Now().UnixMilli()
						linuxPkg.UpdatedTime = time.Now().UnixMilli()
					}
					linuxPkg.Name = releasePkg.Name
					linuxPkg.Description = releasePkg.Description
					linuxPkg.Version = releasePkg.Version
					linuxPkg.Distribution = os
					linuxPkg.Arch = common.NoArch.String()
					if isLinuxPkgExist {
						_, err := linuxPkgRepo.Update(&linuxPkg)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
					} else {
						_, err := linuxPkgRepo.Create(&linuxPkg)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
					}
				}
			}
		}
	}
}

func getChunkWiseNoticeData(offset, size int) (*ubuntu.UbuntuNoticeResponse, error) {
	time.Sleep(5 * time.Second)
	logger.ServiceLogger.Info(fmt.Sprintf("Fetch notice data for offset : %v & size : %v", offset, size))
	noticeURL := "https://ubuntu.com/security/page/notices.json"
	queryParams := url.Values{}
	queryParams.Set("limit", strconv.Itoa(size))
	queryParams.Set("offset", strconv.Itoa(offset))
	queryParams.Set("order", "newest")

	noticeHttpUrl := fmt.Sprintf("%s?%s", noticeURL, queryParams.Encode())

	req, err := http.NewRequest(http.MethodGet, noticeHttpUrl, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Accept", "application/json")

	client := http.DefaultClient
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	var response ubuntu.UbuntuNoticeResponse
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

func (service UbuntuNoticeDataService) GetAllUbuntuNotice(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.UbuntuNoticeData.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []ubuntu.UbuntuNoticeData
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.UbuntuNoticeData.String(), false)
		packagePageList, err = service.Repository.GetAllUbuntuNoticeData(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(packagePageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
