package thirdparty

import (
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"regexp"
	"strings"
	"time"
)

var postgresqlMetaData = map[string]interface{}{
	"uuid": "a1b2c3d4-e5f6-7890-abcd-123456789xyz",
	"templateFileNameMap": map[string]interface{}{
		"x64": "POSTGRESQL_X64.xml",
	},
}

type PostgreSQLPoolingService struct {
	ThirdPartyPackageService
}

func (c PostgreSQLPoolingService) Name() string {
	return "PostgreSQLPoolingService"
}

func init() {
	RegisterCollector(PostgreSQLPoolingService{})
}

func NewPostgreSQLPoolingService() *PostgreSQLPoolingService {
	return &PostgreSQLPoolingService{}
}

func (service PostgreSQLPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching PostgreSQL data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching PostgreSQL data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.POSTGRESQL)
}

func (service *PostgreSQLPoolingService) fetchLatestPatchData() error {
	// Fetch the EDB PostgreSQL download page
	resp, err := http.Get("https://www.enterprisedb.com/downloads/postgres-postgresql-downloads")
	if err != nil {
		return fmt.Errorf("error fetching PostgreSQL download page: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	// Parse the page to find the latest version and download URL
	version, downloadURL, err := service.parsePostgreSQLVersion(string(body))
	if err != nil {
		return fmt.Errorf("error parsing PostgreSQL version: %w", err)
	}

	logger.ServiceLogger.Debug("Found PostgreSQL version: ", version)

	// Create release package if required
	err = service.createReleasePackageIfRequired(version, downloadURL, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service *PostgreSQLPoolingService) parsePostgreSQLVersion(htmlContent string) (string, string, error) {
	// Look for the latest version in the download table
	// Pattern to match version numbers like "17.5", "16.9", etc.
	versionRegex := regexp.MustCompile(`(\d+\.\d+)`)
	
	// Pattern to match download URLs for Windows x64
	downloadRegex := regexp.MustCompile(`https://sbp\.enterprisedb\.com/getfile\.jsp\?fileid=(\d+)`)
	
	versions := versionRegex.FindAllString(htmlContent, -1)
	downloadURLs := downloadRegex.FindAllString(htmlContent, -1)
	
	if len(versions) == 0 {
		return "", "", fmt.Errorf("no PostgreSQL versions found")
	}
	
	if len(downloadURLs) == 0 {
		return "", "", fmt.Errorf("no download URLs found")
	}
	
	// Get the first (latest) version and corresponding download URL
	latestVersion := versions[0]
	downloadURL := downloadURLs[0] // First Windows x64 download URL
	
	return latestVersion, downloadURL, nil
}

func (service *PostgreSQLPoolingService) createReleasePackageIfRequired(version, downloadURL string, osArch common.OsArchitecture) error {
	thirdPartyRepo := service.Repository
	
	// Check if package already exists
	existingPkg, _ := thirdPartyRepo.GetPkgByOsVersionPlatformOsArchApplication("", int(osArch), int(common.Windows), int(common.POSTGRESQL))
	
	if existingPkg.Id > 0 && existingPkg.Version == version {
		logger.ServiceLogger.Debug("PostgreSQL package already exists with version: ", version)
		return nil
	}
	
	logger.ServiceLogger.Debug("New Version found for PostgreSQL ", version, " creating package data")
	
	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}
	
	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := fmt.Sprintf("postgresql-%s-windows-x64.exe", version)
	
	// Use current time as release date since we don't have exact release date
	releaseTime := time.Now()
	
	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.POSTGRESQL)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}
	
	// Create new package
	uuid := fmt.Sprintf("%s_%s_%s", common.POSTGRESQL.String(), version, osArch.String())
	
	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "PostgreSQL",
		},
		Description:      "PostgreSQL is a powerful, open source object-relational database system with over 35 years of active development that has earned it a strong reputation for reliability, feature robustness, and performance.",
		Version:          version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "PostgreSQL Global Development Group",
		SupportUrl:       "https://www.postgresql.org/support/",
		ReleaseNote:      "https://www.postgresql.org/docs/release/",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.POSTGRESQL,
		Uuid:             uuid,
	}
	
	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}
	
	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.POSTGRESQL)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd
	
	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()
	
	_, err := thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating PostgreSQL package: %w", err)
	}
	
	// Generate XML file for Windows
	GenerateXmlForWindows(postgresqlMetaData, version, osArch, uuid, common.POSTGRESQL)
	
	logger.ServiceLogger.Debug("Package data created successfully for PostgreSQL version ", version)
	return nil
}
