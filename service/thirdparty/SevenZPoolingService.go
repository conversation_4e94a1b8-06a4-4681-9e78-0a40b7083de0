package thirdparty

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"strings"
	"time"
)

// GitHubAsset represents a GitHub release asset
type GitHubAsset struct {
	Name               string `json:"name"`
	BrowserDownloadURL string `json:"browser_download_url"`
	Size               int64  `json:"size"`
}

// GitHubRelease represents a GitHub release
type GitHubRelease struct {
	TagName     string        `json:"tag_name"`
	PublishedAt string        `json:"published_at"`
	Assets      []GitHubAsset `json:"assets"`
}

// ApplicationData represents parsed application data from GitHub
type ApplicationData struct {
	TagName            string
	PublishedAt        string
	FileName           string
	BrowserDownloadURL string
	Size               int64
}

var sevenZMetaData = map[string]interface{}{
	"uuid": "b8b8e7e7-7b7b-4c4c-9d9d-1e1e1e1e1e1e",
	"templateFileNameMap": map[string]interface{}{
		"x64": "SEVEN_Z_X64.xml",
	},
}

type SevenZPoolingService struct {
	ThirdPartyPackageService
}

func (c SevenZPoolingService) Name() string {
	return "SevenZPoolingService"
}

func init() {
	RegisterCollector(SevenZPoolingService{})
}

func NewSevenZPoolingService() *SevenZPoolingService {
	return &SevenZPoolingService{}
}

func (service SevenZPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching 7-zip data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching 7-zip data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.SEVEN_Z)
}

func (service SevenZPoolingService) fetchLatestPatchData() error {
	applicationDataList, err := service.parseGitHubLatestReleaseApiData("ip7z", "7zip")
	if err != nil {
		return fmt.Errorf("error parsing GitHub release data: %w", err)
	}

	for _, appData := range applicationDataList {
		if strings.Contains(appData.FileName, "x64") {
			err := service.createReleasePackageIfRequired(appData, common.X64)
			if err != nil {
				logger.ServiceLogger.Error("Error creating x64 release package: ", err)
			}
		}
	}

	return nil
}

func (service SevenZPoolingService) parseGitHubLatestReleaseApiData(owner, repo string) ([]ApplicationData, error) {
	url := fmt.Sprintf("https://api.github.com/repos/%s/%s/releases/latest", owner, repo)

	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Accept", "application/vnd.github.v3+json")
	req.Header.Set("User-Agent", "patch-central-repo")

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var release GitHubRelease
	err = json.Unmarshal(body, &release)
	if err != nil {
		return nil, err
	}

	var applicationDataList []ApplicationData
	for _, asset := range release.Assets {
		if strings.HasSuffix(asset.Name, ".msi") {
			appData := ApplicationData{
				TagName:            release.TagName,
				PublishedAt:        release.PublishedAt,
				FileName:           asset.Name,
				BrowserDownloadURL: asset.BrowserDownloadURL,
				Size:               asset.Size,
			}
			applicationDataList = append(applicationDataList, appData)
		}
	}

	return applicationDataList, nil
}

func (service SevenZPoolingService) createReleasePackageIfRequired(appData ApplicationData, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.SEVEN_Z))
	if existingPkg.Id > 0 && existingPkg.Version == appData.TagName {
		logger.ServiceLogger.Debug("Data already exists for 7z version ", appData.TagName, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for 7z ", appData.TagName, " creating package data")

	// Parse release date
	releaseTime, err := time.Parse(time.RFC3339, appData.PublishedAt)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing release date: ", err)
		releaseTime = time.Now()
	}

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.SEVEN_Z)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := fmt.Sprintf("%s_%s_%s", common.SEVEN_Z.String(), appData.TagName, osArch.String())

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "7-zip",
		},
		Description: `7-Zip is free software with open source.
The most of the code is under the GNU LGPL license.
Some parts of the code are under the BSD 3-clause License.
Also there is unRAR license restriction for some parts of the code`,
		Version:          appData.TagName,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: appData.BrowserDownloadURL,
		Publisher:        "Igor Pavlov",
		SupportUrl:       "https://7-zip.org/",
		ReleaseNote:      "https://7-zip.org/history.txt",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.SEVEN_Z,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    appData.FileName,
		DownloadUrl: appData.BrowserDownloadURL,
		Size:        appData.Size,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.SEVEN_Z)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err = thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating 7z package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(sevenZMetaData, appData.TagName, osArch, uuid, common.SEVEN_Z)

	logger.ServiceLogger.Debug("Package data created successfully for 7z version ", appData.TagName)
	return nil
}
