package thirdparty

import (
	"bytes"
	"crypto/tls"
	"encoding/xml"
	"errors"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"io"
	"net/http"
	"os"
	"os/exec"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

var (
	ReleaseCABUrlPath = "http://officecdn.microsoft.com/pr/wsus/releasehistory.cab"
	OflCABUrl         = "https://officecdn.microsoft.com/pr/wsus/ofl.cab"
	ODTDownloadUrl    = "https://www.microsoft.com/en-us/download/details.aspx?id=49117"
)

type MicrosoftOfficeDataPoolingService struct {
	ThirdPartyPackageService
}

func (c MicrosoftOfficeDataPoolingService) Name() string {
	return "MicrosoftOfficeDataPoolingService"
}
func init() {
	RegisterCollector(MicrosoftOfficeDataPoolingService{})
}

// OfficeUpdateChannel represents an Office update channel
type OfficeUpdateChannel struct {
	Name          string `xml:"Name,attr"`
	ID            string `xml:"ID,attr"`
	DisplayName   string `xml:"DisplayName,attr"`
	Version       string
	LegacyVersion string
	Build         string
	PubTime       string
	SupportURL    string
}

// Update represents an update within a channel
type Update struct {
	Version       string `xml:"Version,attr"`
	LegacyVersion string `xml:"LegacyVersion,attr"`
	Build         string `xml:"Build,attr"`
	PubTime       string `xml:"PubTime,attr"`
	Latest        string `xml:"Latest,attr"`
}

// UpdateChannel represents the XML structure for update channels
type UpdateChannel struct {
	XMLName     xml.Name `xml:"UpdateChannel"`
	Name        string   `xml:"Name,attr"`
	ID          string   `xml:"ID,attr"`
	DisplayName string   `xml:"DisplayName,attr"`
	Updates     []Update `xml:"Update"`
}

// ReleaseHistoryXML represents the root XML structure for release history
type ReleaseHistoryXML struct {
	XMLName  xml.Name        `xml:"ReleaseHistory"`
	Channels []UpdateChannel `xml:"UpdateChannel"`
}

// BaseURL represents a base URL mapping in ofl.cab
type BaseURL struct {
	Branch string `xml:"branch,attr"`
	URL    string `xml:"URL,attr"`
}

// UpdateFiles represents the UpdateFiles element in ofl.cab
type UpdateFiles struct {
	XMLName  xml.Name  `xml:"UpdateFiles"`
	BaseURLs []BaseURL `xml:"baseURL"`
}

func NewMicrosoftOfficeDataPoolingService() *MicrosoftOfficeDataPoolingService {
	return &MicrosoftOfficeDataPoolingService{}
}

func (service MicrosoftOfficeDataPoolingService) ExecuteSync() {
	logger.ServiceLogger.Info("Starting Microsoft Office data pooling service...")

	// Step 1: Download the Office Deployment Tool
	odtDownloadUrl := service.GetOfficeDeploymentToolDownloadURL()
	if odtDownloadUrl == "" {
		logger.ServiceLogger.Error("Failed to get ODT download URL, aborting sync")
		return
	}
	logger.ServiceLogger.Info("Microsoft Office Deployment Tool data created successfully")

	// Step 2: Get Office update channel data by processing CAB files
	officeUpdateChannels := service.getChannelDataList()
	if len(officeUpdateChannels) == 0 {
		logger.ServiceLogger.Warn("No Office update channels found")
		return
	}

	// Step 3: Create or update patches for each channel
	service.createOrUpdatePatches(officeUpdateChannels, odtDownloadUrl)

	// Step 4: Clean up extracted folders
	service.deleteExtractedFolder()

	logger.ServiceLogger.Info("Microsoft Office data pooling service completed successfully")
}

func (service MicrosoftOfficeDataPoolingService) GetOfficeDeploymentToolDownloadURL() string {
	odtDownloadUrl, err := service.GetOfficeDeploymentToolDetails()
	if err != nil {
		logger.ServiceLogger.Error("Error getting Office Deployment Tool details: ", err.Error())
		return ""
	}
	logger.ServiceLogger.Info("Office Deployment Tool download URL: ", odtDownloadUrl)

	fileName := common.FilenameFromUrl(odtDownloadUrl)

	//check if file already exists in the folder ODTDirectoryPath
	filePath := filepath.Join(common.ODTDirectoryPath(), fileName)
	_, err = os.Stat(filePath)
	if !os.IsNotExist(err) {
		logger.ServiceLogger.Info("Office Deployment Tool already exists in the folder: ", filePath)
		return odtDownloadUrl
	} else {
		logger.ServiceLogger.Info("Office Deployment Tool does not exist in the folder: ", filePath)
		err = os.RemoveAll(common.ODTDirectoryPath())
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	//download odt file and store in the folder ODTDirectoryPath
	common.DownloadFileAndCopyToFilePath(odtDownloadUrl, common.ODTDirectoryPath(), fileName)
	return odtDownloadUrl
}

// GetOfficeDeploymentToolDetails scrapes the Office Deployment Tool download page to get the download URL and other details
func (service MicrosoftOfficeDataPoolingService) GetOfficeDeploymentToolDetails() (string, error) {
	// Get the HTML content from the ODT download page
	htmlContent, err := service.fetchHTMLContent(ODTDownloadUrl)
	if err != nil {
		return "", err
	}

	// Parse the HTML content using goquery
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return "", err
	}

	// Extract the download URL
	var downloadURL string
	doc.Find("a[href*='download.microsoft.com']").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if exists && strings.Contains(href, "download.microsoft.com") {
			downloadURL = href
			return
		}
	})

	// If no direct download link found, try looking for the Download button
	if downloadURL == "" {
		doc.Find("a.mscom-link.download-button").Each(func(i int, s *goquery.Selection) {
			href, exists := s.Attr("href")
			if exists {
				downloadURL = href
				return
			}
		})
	}

	// If still no download URL found, try any Download link
	if downloadURL == "" {
		doc.Find("a").Each(func(i int, s *goquery.Selection) {
			text := strings.ToLower(s.Text())
			if strings.Contains(text, "download") {
				href, exists := s.Attr("href")
				if exists {
					downloadURL = href
					return
				}
			}
		})
	}

	if downloadURL == "" {
		return "", errors.New("could not find Office Deployment Tool download URL")
	}
	return downloadURL, nil

}

// fetchHTMLContent retrieves the HTML content from the given URL
func (service MicrosoftOfficeDataPoolingService) fetchHTMLContent(url string) (string, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}
	req, err := http.NewRequest("GET", url, nil)

	if err != nil {
		return "", err
	}

	// Set a user agent to avoid being blocked
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	res, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(res.Body)

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

// getChannelDataList downloads and processes both CAB files to get Office update channel data
func (service MicrosoftOfficeDataPoolingService) getChannelDataList() []OfficeUpdateChannel {
	var officeUpdateChannels []OfficeUpdateChannel

	// Step 1: Download and process ofl.cab to get branch URL mappings
	branchURLMap := service.downloadAndProcessOflCAB()

	// Step 2: Download and process releasehistory.cab to get update channels
	officeUpdateChannels = service.downloadAndProcessReleaseHistoryCAB(branchURLMap)

	return officeUpdateChannels
}

// downloadAndProcessOflCAB downloads and processes the ofl.cab file
func (service MicrosoftOfficeDataPoolingService) downloadAndProcessOflCAB() map[string]string {
	branchURLMap := make(map[string]string)

	downloadDir := common.ODTDirectoryPath()
	cabFile := service.downloadCabFile(OflCABUrl, downloadDir)
	if cabFile == "" {
		logger.ServiceLogger.Error("Failed to download ofl.cab file")
		return branchURLMap
	}

	// Extract the CAB file
	extractedFiles := service.extractCabFile(cabFile, downloadDir)
	if len(extractedFiles) == 0 {
		logger.ServiceLogger.Warn("No files extracted from ofl.cab")
		return branchURLMap
	}

	// Find and read the first XML file
	for _, file := range extractedFiles {
		if strings.HasSuffix(strings.ToLower(file), ".xml") && strings.Contains(strings.ToLower(file), "64bit") {
			logger.ServiceLogger.Info("Found XML file for ofl.cab: ", file)
			branchURLMap = service.readOflXMLFile(file)
			logger.ServiceLogger.Info("Extracted ", len(branchURLMap), " branch-URL mappings from ofl.cab XML file")
			break
		}
	}

	return branchURLMap
}

// downloadAndProcessReleaseHistoryCAB downloads and processes the releasehistory.cab file
func (service MicrosoftOfficeDataPoolingService) downloadAndProcessReleaseHistoryCAB(branchURLMap map[string]string) []OfficeUpdateChannel {
	var officeUpdateChannels []OfficeUpdateChannel

	downloadDir := common.ODTDirectoryPath()
	cabFile := service.downloadCabFile(ReleaseCABUrlPath, downloadDir)
	if cabFile == "" {
		logger.ServiceLogger.Error("Failed to download releasehistory.cab file")
		return officeUpdateChannels
	}

	// Extract the CAB file
	extractedFiles := service.extractCabFile(cabFile, downloadDir)
	if len(extractedFiles) == 0 {
		logger.ServiceLogger.Warn("No files extracted from releasehistory.cab")
		return officeUpdateChannels
	}

	// Find and read the first XML file
	for _, file := range extractedFiles {
		if strings.HasSuffix(strings.ToLower(file), ".xml") {
			logger.ServiceLogger.Info("Found XML file for releasehistory.cab: ", file)
			officeUpdateChannels = service.readReleaseHistoryXMLFile(file, branchURLMap)
			break
		}
	}

	return officeUpdateChannels
}

// downloadCabFile downloads a CAB file from the specified URL
func (service MicrosoftOfficeDataPoolingService) downloadCabFile(cabURL, downloadDir string) string {
	// Extract filename from URL
	fileName := filepath.Base(cabURL)
	if fileName == "" || fileName == "." {
		if strings.Contains(cabURL, "ofl.cab") {
			fileName = "ofl.cab"
		} else {
			fileName = "releasehistory.cab"
		}
	}

	logger.ServiceLogger.Info("Downloading Office CAB file from URL: ", cabURL, ", Extracted filename: ", fileName)

	// Create directory if it doesn't exist
	err := os.MkdirAll(downloadDir, 0755)
	if err != nil {
		logger.ServiceLogger.Error("Error creating download directory: ", err.Error())
		return ""
	}

	// Download the file
	cabFilePath := filepath.Join(downloadDir, fileName)
	logger.ServiceLogger.Info("Downloading Office CAB file to: ", cabFilePath)

	// Remove existing file if it exists
	if _, err := os.Stat(cabFilePath); !os.IsNotExist(err) {
		err = os.Remove(cabFilePath)
		if err != nil {
			logger.ServiceLogger.Error("Error removing existing CAB file: ", err.Error())
		}
	}

	success := common.DownloadFileAndCopyToFilePath(cabURL, downloadDir, fileName)
	if !success {
		logger.ServiceLogger.Error("Failed to download Office CAB file")
		return ""
	}

	// Verify file exists
	if _, err := os.Stat(cabFilePath); os.IsNotExist(err) {
		logger.ServiceLogger.Error("Downloaded CAB file does not exist: ", cabFilePath)
		return ""
	}

	logger.ServiceLogger.Info("Successfully downloaded Office CAB file: ", fileName)
	return cabFilePath
}

// extractCabFile extracts a CAB file using 7zip and returns the list of extracted files
func (service MicrosoftOfficeDataPoolingService) extractCabFile(cabFilePath, extractDir string) []string {
	var extractedFiles []string

	logger.ServiceLogger.Info("Extracting CAB file: ", cabFilePath)

	// Create extraction directory
	extractionDir := filepath.Join(extractDir, "extracted")
	err := os.MkdirAll(extractionDir, 0755)
	if err != nil {
		logger.ServiceLogger.Error("Error creating extraction directory: ", err.Error())
		return extractedFiles
	}

	// Copy CAB file to extraction directory if not already there
	cabFileName := filepath.Base(cabFilePath)
	cabFileInExtractionDir := filepath.Join(extractionDir, cabFileName)
	if cabFileInExtractionDir != cabFilePath {
		input, err := os.Open(cabFilePath)
		if err != nil {
			logger.ServiceLogger.Error("Error opening source CAB file: ", err.Error())
			return extractedFiles
		}
		defer input.Close()

		output, err := os.Create(cabFileInExtractionDir)
		if err != nil {
			logger.ServiceLogger.Error("Error creating destination CAB file: ", err.Error())
			return extractedFiles
		}
		defer output.Close()

		_, err = io.Copy(output, input)
		if err != nil {
			logger.ServiceLogger.Error("Error copying CAB file: ", err.Error())
			return extractedFiles
		}
	}

	// Determine the 7z command based on the platform
	command := "7z"
	if runtime.GOOS == "windows" {
		command = "C:\\Program Files\\7-Zip\\7z.exe"
	}

	// Extract the CAB file using 7zip
	args := []string{"x", cabFileInExtractionDir, "-o" + extractionDir}
	logger.ServiceLogger.Info("Executing command: ", command, " ", strings.Join(args, " "))

	cmd := exec.Command(command, args...)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err = cmd.Run()
	if err != nil {
		logger.ServiceLogger.Error("Error extracting CAB file: ", err.Error())
		logger.ServiceLogger.Error("Stderr: ", stderr.String())
		return extractedFiles
	}

	logger.ServiceLogger.Debug("Command result: ", stdout.String())

	// Get the list of extracted files
	entries, err := os.ReadDir(extractionDir)
	if err != nil {
		logger.ServiceLogger.Error("Error reading extraction directory: ", err.Error())
		return extractedFiles
	}

	for _, entry := range entries {
		if !entry.IsDir() && entry.Name() != cabFileName {
			extractedFiles = append(extractedFiles, filepath.Join(extractionDir, entry.Name()))
		}
	}

	logger.ServiceLogger.Info("Extracted ", len(extractedFiles), " files from the CAB file")
	return extractedFiles
}

// readOflXMLFile reads the XML file from ofl.cab and extracts branch-URL mappings
func (service MicrosoftOfficeDataPoolingService) readOflXMLFile(xmlFilePath string) map[string]string {
	branchURLMap := make(map[string]string)

	logger.ServiceLogger.Info("Reading ofl.cab XML file: ", xmlFilePath)

	// Read the XML file
	xmlData, err := os.ReadFile(xmlFilePath)
	if err != nil {
		logger.ServiceLogger.Error("Error reading XML file: ", err.Error())
		return branchURLMap
	}
	// Parse the XML
	branchURLMap = service.createBranchURLMapFromXMLContent(string(xmlData))
	return branchURLMap
}

// createBranchURLMapFromXMLContent creates a branch-wise URL map from the provided OFL XML content
func (service MicrosoftOfficeDataPoolingService) createBranchURLMapFromXMLContent(xmlContent string) map[string]string {
	branchURLMap := make(map[string]string)

	logger.ServiceLogger.Info("Creating branch-wise URL map from provided XML content")

	// Parse the XML content
	var updateFiles UpdateFiles
	err := xml.Unmarshal([]byte(xmlContent), &updateFiles)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing provided XML content: ", err.Error())
		return branchURLMap
	}

	logger.ServiceLogger.Info("Found ", len(updateFiles.BaseURLs), " baseURL elements in the provided XML content")

	// Extract branch-URL mappings
	for _, baseURL := range updateFiles.BaseURLs {
		if baseURL.Branch != "" && baseURL.URL != "" {
			logger.ServiceLogger.Debug("Found branch: ", baseURL.Branch, " with URL: ", baseURL.URL)
			branchURLMap[baseURL.Branch] = baseURL.URL
		}
	}

	logger.ServiceLogger.Info("Successfully created branch-wise URL map with ", len(branchURLMap), " entries")
	return branchURLMap
}

// CreateBranchURLMapFromXML is a standalone utility function to create branch-wise URL map from any OFL XML content
// This can be used independently without creating a service instance
func CreateBranchURLMapFromXML(xmlContent string) (map[string]string, error) {
	branchURLMap := make(map[string]string)

	// Parse the XML content
	var updateFiles UpdateFiles
	err := xml.Unmarshal([]byte(xmlContent), &updateFiles)
	if err != nil {
		return branchURLMap, fmt.Errorf("error parsing XML content: %w", err)
	}

	// Extract branch-URL mappings
	for _, baseURL := range updateFiles.BaseURLs {
		if baseURL.Branch != "" && baseURL.URL != "" {
			branchURLMap[baseURL.Branch] = baseURL.URL
		}
	}

	return branchURLMap, nil
}

// readReleaseHistoryXMLFile reads the XML file from releasehistory.cab and extracts update channels
func (service MicrosoftOfficeDataPoolingService) readReleaseHistoryXMLFile(xmlFilePath string, branchURLMap map[string]string) []OfficeUpdateChannel {
	var officeUpdateChannels []OfficeUpdateChannel

	logger.ServiceLogger.Info("Reading releasehistory.cab XML file: ", xmlFilePath)

	// Read the XML file
	xmlData, err := os.ReadFile(xmlFilePath)
	if err != nil {
		logger.ServiceLogger.Error("Error reading XML file: ", err.Error())
		return officeUpdateChannels
	}

	// Parse the XML
	var releaseHistoryXML ReleaseHistoryXML
	err = xml.Unmarshal(xmlData, &releaseHistoryXML)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing releasehistory.cab XML: ", err.Error())
		return officeUpdateChannels
	}

	logger.ServiceLogger.Info("XML Root element: ", releaseHistoryXML.XMLName.Local)
	logger.ServiceLogger.Info("Found ", len(releaseHistoryXML.Channels), " UpdateChannel elements in the XML file")

	// Process each update channel
	for _, channel := range releaseHistoryXML.Channels {
		officeUpdateChannel := OfficeUpdateChannel{
			Name:        channel.Name,
			ID:          channel.ID,
			DisplayName: channel.DisplayName,
		}

		// Set support URL from branch URL map
		if len(branchURLMap) > 0 {
			if supportURL, exists := branchURLMap[channel.ID]; exists {
				officeUpdateChannel.SupportURL = supportURL
			}
		}

		// Find the latest update
		for _, update := range channel.Updates {
			if strings.EqualFold(update.Latest, "True") {
				officeUpdateChannel.Version = update.Version
				officeUpdateChannel.LegacyVersion = update.LegacyVersion
				officeUpdateChannel.Build = update.Build
				officeUpdateChannel.PubTime = update.PubTime
				break
			}
		}

		officeUpdateChannels = append(officeUpdateChannels, officeUpdateChannel)
	}

	return officeUpdateChannels
}

// createOrUpdatePatches creates or updates patches for each Office update channel
func (service MicrosoftOfficeDataPoolingService) createOrUpdatePatches(officeUpdateChannels []OfficeUpdateChannel, odtDownloadURL string) {
	logger.ServiceLogger.Info("Processing ", len(officeUpdateChannels), " Office update channels")

	for _, channel := range officeUpdateChannels {
		if channel.Version == "" || channel.PubTime == "" {
			logger.ServiceLogger.Warn("Skipping channel ", channel.DisplayName, " due to missing version or publication time")
			continue
		}

		uuid := channel.ID + "@__@" + channel.LegacyVersion
		logger.ServiceLogger.Info("Processing Office channel: ", channel.DisplayName, " version: ", channel.LegacyVersion, " UUID: ", uuid)

		// Parse publication time
		var pubDate time.Time
		var err error

		// Try different time formats
		timeFormats := []string{
			"2006-01-02T15:04:05.000Z",
			"2006-01-02T15:04:05Z",
			time.RFC3339,
		}

		for _, format := range timeFormats {
			pubDate, err = time.Parse(format, channel.PubTime)
			if err == nil {
				break
			}
		}

		if err != nil {
			logger.ServiceLogger.Error("Failed to parse publication time ", channel.PubTime, " for channel ", channel.DisplayName, ": ", err.Error())
			continue
		}

		// Create patch title
		patchTitle := fmt.Sprintf("Update of office 365 (%s) version (%s) (build (%s))",
			channel.DisplayName, channel.LegacyVersion, channel.Build)

		// Log the patch information
		logger.ServiceLogger.Info("Creating/updating release package for Office channel: ", channel.DisplayName, " version: ", channel.LegacyVersion)
		logger.ServiceLogger.Debug("Patch details - UUID: ", uuid, ", Title: ", patchTitle, ", Release Date: ", pubDate.Format(time.RFC3339))
		logger.ServiceLogger.Debug("Download URL: ", odtDownloadURL, ", Support URL: ", channel.SupportURL)

		//Create ThirdPartyPackage from OfficeUpdateChannel
		pkg := thirdparty.ThirdPartyPackage{}
		pkg.Name = patchTitle
		pkg.Description = "Microsoft Office " + channel.DisplayName + " " + channel.LegacyVersion
		pkg.Version = channel.LegacyVersion
		pkg.Os = common.Windows
		pkg.Arch = common.X64
		pkg.LanguageCode = "en"
		pkg.LatestPackageUrl = odtDownloadURL
		pkg.Publisher = "Microsoft"
		pkg.SupportUrl = channel.SupportURL
		pkg.ProductCode = channel.SupportURL
		pkg.ReleaseNote = ""
		pkg.ReleaseDate = pubDate.UnixMilli()
		pkg.Application = common.MICROSOFT_OFFICE
		pkg.Uuid = uuid
		pkg.PkgFileData = []model.FileData{
			{
				FileName:    "OfficeDataTool.exe",
				DownloadUrl: odtDownloadURL,
				Size:        common.GetFileSizeFromUrl(odtDownloadURL),
				ReleaseDate: pubDate.UnixMilli(),
			},
		}

		// Generate install and uninstall commands
		installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.MICROSOFT_OFFICE)
		pkg.InstallCommand = installCmd
		pkg.UnInstallCommand = uninstallCmd

		pkg.UpdatedTime = time.Now().UnixMilli()
		pkg.CreatedTime = time.Now().UnixMilli()

		// Check if package already exists
		existingPkg := NewThirdPartyPackageService().Repository.GetByUUId(uuid)
		if existingPkg.Id > 0 {
			pkg.Id = existingPkg.Id
			_, err = NewThirdPartyPackageService().Repository.Update(&pkg)
			if err != nil {
				logger.ServiceLogger.Error("Error while updating Office package for pkg : ", err)
			}
		} else {
			// Delete all existing packages for this channel
			uuidLike := "%" + channel.ID + "@__@%"
			NewThirdPartyPackageService().Repository.DeletePatchByUUIDLike(uuidLike)

			_, err = NewThirdPartyPackageService().Repository.Create(&pkg)
			if err != nil {
				logger.ServiceLogger.Error("Error while creating Office package for pkg : ", err)
			}
		}
		logger.ServiceLogger.Info("Successfully processed release package for Office channel: ", channel.DisplayName, " version: ", channel.Version)
	}
}

// deleteExtractedFolder deletes the extracted folder after processing
func (service MicrosoftOfficeDataPoolingService) deleteExtractedFolder() {
	downloadDir := common.ODTDirectoryPath()
	extractionDir := filepath.Join(downloadDir, "extracted")

	if _, err := os.Stat(extractionDir); !os.IsNotExist(err) {
		logger.ServiceLogger.Info("Deleting extracted folder: ", extractionDir)
		err = os.RemoveAll(extractionDir)
		if err != nil {
			logger.ServiceLogger.Error("Error deleting extracted folder: ", err.Error())
		} else {
			logger.ServiceLogger.Info("Successfully deleted extracted folder")
		}
	} else {
		logger.ServiceLogger.Debug("Extracted folder does not exist: ", extractionDir)
	}
}
