package thirdparty

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"strings"
	"time"
)

var notepadPlusPlusMetaData = map[string]interface{}{
	"uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
	"templateFileNameMap": map[string]interface{}{
		"x64": "NOTEPAD_PLUS_PLUS_X64.xml",
	},
}

type NotePadPlusPlusPoolingService struct {
	ThirdPartyPackageService
}

func (c NotePadPlusPlusPoolingService) Name() string {
	return "NotePadPlusPlusPoolingService"
}

func init() {
	RegisterCollector(NotePadPlusPlusPoolingService{})
}

func NewNotePadPlusPlusPoolingService() *NotePadPlusPlusPoolingService {
	return &NotePadPlusPlusPoolingService{}
}

func (service NotePadPlusPlusPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching Notepad++ data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching Notepad++ data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.NOTEPAD_PLUS_PLUS)
}

func (service NotePadPlusPlusPoolingService) fetchLatestPatchData() error {
	applicationDataList, err := service.parseGitHubLatestReleaseApiData("notepad-plus-plus", "notepad-plus-plus")
	if err != nil {
		return fmt.Errorf("error parsing GitHub release data: %w", err)
	}

	for _, appData := range applicationDataList {
		if strings.Contains(appData.FileName, "x64") {
			err := service.createReleasePackageIfRequired(appData, common.X64)
			if err != nil {
				logger.ServiceLogger.Error("Error creating x64 release package: ", err)
			}
		}
	}

	return nil
}

func (service NotePadPlusPlusPoolingService) parseGitHubLatestReleaseApiData(owner, repo string) ([]ApplicationData, error) {
	url := fmt.Sprintf("https://api.github.com/repos/%s/%s/releases/latest", owner, repo)

	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Accept", "application/vnd.github.v3+json")
	req.Header.Set("User-Agent", "patch-central-repo")

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var release GitHubRelease
	err = json.Unmarshal(body, &release)
	if err != nil {
		return nil, err
	}

	var applicationDataList []ApplicationData
	for _, asset := range release.Assets {
		if strings.HasSuffix(asset.Name, ".exe") {
			appData := ApplicationData{
				TagName:            release.TagName,
				PublishedAt:        release.PublishedAt,
				FileName:           asset.Name,
				BrowserDownloadURL: asset.BrowserDownloadURL,
				Size:               asset.Size,
			}
			applicationDataList = append(applicationDataList, appData)
		}
	}

	return applicationDataList, nil
}

func (service NotePadPlusPlusPoolingService) createReleasePackageIfRequired(appData ApplicationData, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.NOTEPAD_PLUS_PLUS))
	if existingPkg.Id > 0 && existingPkg.Version == appData.TagName {
		logger.ServiceLogger.Debug("Data already exists for Notepad++ version ", appData.TagName, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for Notepad++ ", appData.TagName, " creating package data")

	// Parse release date
	releaseTime, err := time.Parse(time.RFC3339, appData.PublishedAt)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing release date: ", err)
		releaseTime = time.Now()
	}

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.NOTEPAD_PLUS_PLUS)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := fmt.Sprintf("%s_%s_%s", common.NOTEPAD_PLUS_PLUS.String(), appData.TagName, osArch.String())

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "Notepad++",
		},
		Description: `Notepad++ is a free source code editor and Notepad replacement that supports several languages.
Running in the MS Windows environment, its use is governed by GNU General Public License.`,
		Version:          appData.TagName,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: appData.BrowserDownloadURL,
		Publisher:        "Don Ho",
		SupportUrl:       "https://notepad-plus-plus.org/",
		ReleaseNote:      "https://notepad-plus-plus.org/downloads/",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.NOTEPAD_PLUS_PLUS,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    appData.FileName,
		DownloadUrl: appData.BrowserDownloadURL,
		Size:        appData.Size,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.NOTEPAD_PLUS_PLUS)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err = thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating Notepad++ package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(notepadPlusPlusMetaData, appData.TagName, osArch, uuid, common.NOTEPAD_PLUS_PLUS)

	logger.ServiceLogger.Debug("Package data created successfully for Notepad++ version ", appData.TagName)
	return nil
}
