package windows

import (
	"patch-central-repo/common"
	windows2 "patch-central-repo/model/windows"
	"patch-central-repo/repository/windows"
	"patch-central-repo/rest"
)

type CabSyncHistoryService struct {
	Repository *windows.CabSyncHistoryRepository
}

func NewCabSyncHistoryService() *CabSyncHistoryService {
	return &CabSyncHistoryService{
		Repository: windows.NewCabSyncHistoryRepository(),
	}
}

func (service CabSyncHistoryService) GetAllWindowsCabHistory(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var historyPageList []windows2.CabSyncHistory
	var err error
	qualification := filter.Qualification
	if qualification == nil || len(qualification) == 0 {
		historyPageList, err = service.Repository.GetAllCabHistory("")
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = historyPageList
		responsePage.TotalCount = len(historyPageList)
	} else {
		countQuery := rest.PrepareQueryFromSearchFilter(filter, common.CabSyncHistory.String(), true)
		count := service.Repository.Count(countQuery)
		if count > 0 {
			searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.CabSyncHistory.String(), false)
			historyPageList, err = service.Repository.GetAllCabHistory(searchQuery)
			if err != nil {
				return responsePage, err
			}
			responsePage.ObjectList = historyPageList
		} else {
			responsePage.ObjectList = make([]interface{}, 0)
		}
		responsePage.TotalCount = count
	}

	return responsePage, nil
}
