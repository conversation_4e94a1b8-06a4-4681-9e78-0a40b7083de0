package windows

import (
	"database/sql"
	"errors"
	"fmt"
	_ "github.com/denisenkom/go-mssqldb"
	"patch-central-repo/common"
	"patch-central-repo/logger"
)

type WsusService struct {
	WsusServer   string
	WsusPort     string
	WsusUser     string
	WsusPassword string
	WsusDatabase string
}

var WsusConn *sql.DB

const (
	GET_PATCH_UPDATEID_LIST_QUERY          = "SELECT p.Updateid from PUBLIC_VIEWS.vPatchDetails p  where p.updateid in (%s);"
	GET_PATCH_LIST_QUERY_BY_UPDATE_ID_IN   = "SELECT * from PUBLIC_VIEWS.vPatchDetails p  where p.updateid in (%s);"
	GET_PATCH_DETAILS_BY_LAST_UPDATED_DATE = "SELECT * from PUBLIC_VIEWS.vPatchDetails p  where p.LastUpdatedTime > '%s' ORDER BY p.LastUpdatedTime ASC OFFSET %d ROWS FETCH NEXT %d ROWS ONLY;"
	GET_AFFECTED_PRODUCT_BY_REVISION_ID    = "SELECT DISTINCT tu.UpdateID,tlp2.Title, tp4.UpdateType, tu.DetectoidType,tr.RevisionID from dbo.tbPrerequisiteDependency tpd inner join tbPrerequisite tp on tp.RevisionID =tpd.RevisionID inner join tbInstalledUpdateSufficientForPrerequisite ip on ip.PrerequisiteID =tp.PrerequisiteID inner join tbUpdate tu on tu.LocalUpdateID = ip.LocalUpdateID inner join tbRevision tr on tr.LocalUpdateID =tu.LocalUpdateID inner join tbProperty tp4  on tp4.RevisionID = tr.RevisionID inner join tbLocalizedPropertyForRevision tlpfr2 on tlpfr2.RevisionID = tp4.RevisionID and tlpfr2 .LanguageID = tp4.DefaultPropertiesLanguageID inner join tbLocalizedProperty tlp2 on tlp2.LocalizedPropertyID = tlpfr2 .LocalizedPropertyID where tpd.RevisionID = %d;"
	GET_PRODUCT_BY_UUID                    = "SELECT DISTINCT  vc.CategoryType as cattype,convert(nvarchar(50),vu.UpdateID) as uuid,vc.DefaultTitle as productname, convert(nvarchar(50),vc.CategoryId) as CategoryId from PUBLIC_VIEWS.vUpdateInCategory vu inner join PUBLIC_VIEWS.vCategory vc on vc.CategoryId =vu.CategoryId and vu.UpdateId IN (%s);"

	GET_ALL_PATCH_CATEGORY = "SELECT * from PUBLIC_VIEWS.vPatchCategory vc order by vc.CategoryIndex  asc;"

	GET_FILE_DETAILS_BY_REVISION_ID = "SELECT trl.LanguageID as lang, tf.FileName as filename,tf.MUURL as url ,tf.[Size] as fsize from dbo.tbFileForRevision tfr inner join dbo.tbRevisionLanguage trl on tfr.RevisionID =trl.RevisionID and trl.Expanded =0 INNER JOIN dbo.tbFile tf ON tf.FileDigest  = tfr.FileDigest and tf.IsEula =0  and tfr.PatchingType !=2 and trl.RevisionID = %s;"

	GET_XML_FOR_UPDATE_ID = "SELECT x.RootElementXml , ISNULL(datalength(RootElementXmlCompressed), 0) as XmlSize, RootElementXmlCompressed FROM dbo.tbXml x INNER JOIN dbo.tbRevision r ON x.RevisionID = r.RevisionID INNER JOIN dbo.tbUpdate u ON r.LocalUpdateID = u.LocalUpdateID WHERE x.RootElementType = 0 and u.UpdateID = '%s'"

	VIEWS_PATCH_LIST_UUID = "SELECT p.UpdateId from PUBLIC_VIEWS.vPatchDetails p;"

	GET_ALL_MISSING_KB_FROM_WINDOWS_PATH = "SELECT lower(x.uuid) FROM public.wsuseuuid x EXCEPT select lower(kb.uuid)from public.windows_patches kb;"

	GET_ALL_LANGUAGE = "SELECT  * from dbo.tbLanguage l;"
)

func NewWsusService() *WsusService {
	return &WsusService{
		WsusServer:   common.GetEnv("WsusServer", "192.168.1.50"),
		WsusPort:     common.GetEnv("WsusPort", "1433"),
		WsusUser:     common.GetEnv("WsusUser", "zirozen"),
		WsusPassword: common.GetEnv("WsusPassword", "Ziro@ziro@2019"),
		WsusDatabase: common.GetEnv("WsusDatabase", "SUSDB"),
	}
}

func (service WsusService) Init() (bool, *sql.DB) {
	if WsusConn == nil {
		connString := fmt.Sprintf("server=%s;user id=%s;password=%s;port=%s;database=%s",
			service.WsusServer, service.WsusUser, service.WsusPassword, service.WsusPort, service.WsusDatabase)

		conn, err := sql.Open("sqlserver", connString)
		if err != nil {
			logger.ServiceLogger.Error("Error connecting to database:", err.Error())
			return false, nil
		}

		err = conn.Ping()
		if err != nil {
			logger.ServiceLogger.Error("Error pinging database:", err.Error())
			return false, nil
		}

		WsusConn = conn
	}

	return true, WsusConn
}

func (service WsusService) RunQuery(query string) ([]map[string]interface{}, error) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error(err)
		}
	}()
	isConnected, dbConn := service.Init()
	if isConnected {
		rows, err := dbConn.Query(query)
		if err != nil {
			err = WsusConn.Close()
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
			WsusConn = nil
			return nil, err
		}

		columns, err := rows.Columns()
		if err != nil {
			err = WsusConn.Close()
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
			WsusConn = nil
			return nil, err
		}

		var result []map[string]interface{}

		for rows.Next() {
			valuePointers := make([]interface{}, len(columns))
			values := make([]interface{}, len(columns))

			for i := range columns {
				valuePointers[i] = &values[i]
			}

			if err := rows.Scan(valuePointers...); err != nil {
				err = WsusConn.Close()
				if err != nil {
					logger.ServiceLogger.Error(err)
				}
				WsusConn = nil
				return nil, err
			}

			rowMap := make(map[string]interface{})
			for i, col := range columns {
				rowMap[col] = values[i]
			}

			result = append(result, rowMap)
		}

		return result, nil
	}
	err := WsusConn.Close()
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
	WsusConn = nil
	return nil, errors.New("db connection close")
}
