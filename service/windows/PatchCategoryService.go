package windows

import (
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
	"patch-central-repo/repository"
	"patch-central-repo/rest"
	"strings"
	"time"
)

type PatchCategoryService struct {
	Repository *repository.PatchCategoryRepository
}

func NewPatchCategoryService() *PatchCategoryService {
	return &PatchCategoryService{
		Repository: repository.NewPatchCategoryRepository(),
	}
}

func (service PatchCategoryService) PoolCategoryFromWSUS() {
	wsusService := NewWsusService()
	result, err := wsusService.RunQuery(GET_ALL_PATCH_CATEGORY)
	if err != nil {
		logger.ServiceLogger.Error("Error while executing GET_ALL_PATCH_CATEGORY :", err)
		return
	}

	if result != nil && len(result) > 0 {
		for _, categoryMap := range result {
			pchCategory := windows.PatchCategory{}
			if val, ok := categoryMap["CategoryId"]; ok && val != nil {
				pchCategory.UUID = val.(string)
				category, _ := service.Repository.FindByUUID(pchCategory.UUID)
				if category.Id != 0 {
					continue
				}
			}
			if val, ok := categoryMap["ParentCategoryId"]; ok && val != nil {
				pchCategory.ParentUUID = val.(string)
				category, _ := service.Repository.FindByUUID(pchCategory.ParentUUID)
				if category.Id != 0 {
					pchCategory.ParentId = category.Id
					pchCategory.ParentName = category.Name
				}
			}
			if val, ok := categoryMap["CategoryType"]; ok && val != nil {
				pchCategory.CategoryType = val.(string)
			}
			if val, ok := categoryMap["DefaultTitle"]; ok && val != nil {
				pchCategory.Name = strings.ReplaceAll(val.(string), ".", "")
			}
			if val, ok := categoryMap["DefaultDescription"]; ok && val != nil {
				pchCategory.Description = strings.ReplaceAll(val.(string), ".", "")
			}
			pchCategory.CreatedTime = time.Now().UnixMilli()
			pchCategory.UpdatedTime = time.Now().UnixMilli()
			_, err := service.Repository.Create(&pchCategory)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}
	}
}

func (service PatchCategoryService) GetAllCategory(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.PatchCategory.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []windows.PatchCategory
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.PatchCategory.String(), false)
		packagePageList, err = service.Repository.GetAllCategory(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
