package windows

import (
	"crypto/tls"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"io"
	"net/http"
	"net/url"
	"patch-central-repo/logger"
	"strconv"
	"strings"
)

type UpdateCatalogService struct {
	UpdateCatalogUrl string
}

func NewUpdateCatalogService() *UpdateCatalogService {
	return &UpdateCatalogService{
		UpdateCatalogUrl: "https://www.catalog.update.microsoft.com/Search.aspx",
	}
}

func (service UpdateCatalogService) ProcessUpdateCatalogDataByQueryList(queryList []string) {
	var patchDetailList []string
	for _, query := range queryList {
		patchDetails := service.SearchUpdateCatalogByQuery(query)
		patchDetailList = append(patchDetailList, patchDetails...)
	}
	logger.ServiceLogger.Debug("[ProcessUpdateCatalogDataByQueryList] query : ", queryList)
	logger.ServiceLogger.Debug("[ProcessUpdateCatalogDataByQueryList] Total uuids found : ", len(patchDetailList))

	widowsPatchService := NewWindowsPatchService()
	for _, uuid := range patchDetailList {
		widowsPatchService.processFileData(uuid)
	}
}

func (service UpdateCatalogService) SearchUpdateCatalogByQuery(query string) []string {
	var patchDataList []string

	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	Uri := service.UpdateCatalogUrl + "?q=" + url.QueryEscape(query)
	response, err := client.Get(Uri)
	if err != nil {
		logger.ServiceLogger.Error("[SearchUpdateCatalogByQuery] Error : ", err)
		return patchDataList
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(response.Body)

	document, err := goquery.NewDocumentFromReader(response.Body)
	if err != nil {
		logger.ServiceLogger.Error("[SearchUpdateCatalogByQuery] Error while read NewDocumentFromReader : ", err)
		return patchDataList
	}

	numberOfPages := 0
	paginationText := document.Find("#ctl00_catalogBody_searchDuration").Text()
	if paginationText != "" {
		paginationText := strings.Split(paginationText, "page")[1]
		numberOfPageDetails := strings.TrimSpace(strings.Split(paginationText, "of")[1])
		fmt.Sscanf(numberOfPageDetails, "%d", &numberOfPages)
		numberOfPages--
	}

	for numberOfPages >= 0 {
		patchDataList = append(patchDataList, parseSearchPageData(document)...)
		if numberOfPages >= 0 {
			response, err := client.Get(Uri + "&p=" + strconv.Itoa(numberOfPages))
			if err != nil {
				logger.ServiceLogger.Error("Error :", err)
				break
			}

			document, err = goquery.NewDocumentFromReader(response.Body)
			if err != nil {
				logger.ServiceLogger.Error("Error :", err)
				break
			}

			err = response.Body.Close()
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}
		numberOfPages--
	}

	return patchDataList
}

func parseSearchPageData(document *goquery.Document) []string {
	var uuids []string
	if document.Find("#ctl00_catalogBody_updateMatches").Size() > 0 {
		document.Find("#ctl00_catalogBody_updateMatches tr").Each(func(i int, row *goquery.Selection) {
			if i > 0 {
				uuidData := strings.Split(row.AttrOr("id", ""), "_")
				uuid := uuidData[0]
				rowNumber := uuidData[1]
				classification := document.Find("#" + uuid + "_C3_" + rowNumber).Text()
				if !strings.Contains(strings.ToLower(classification), "driver") {
					uuids = append(uuids, uuid)
				}
			}
		})
	}
	return uuids
}
