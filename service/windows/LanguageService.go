package windows

import (
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model/windows"
	"patch-central-repo/repository"
	"patch-central-repo/rest"
	"time"
)

type LanguageService struct {
	Repository *repository.LanguageRepository
}

func NewLanguageService() *LanguageService {
	return &LanguageService{
		Repository: repository.NewLanguageRepository(),
	}
}

func (service *LanguageService) SyncLanguageFromWsus() {
	resultMapList, err := NewWsusService().RunQuery(GET_ALL_LANGUAGE)
	if err != nil {
		logger.ServiceLogger.Error(err)
		return
	}

	if len(resultMapList) > 0 {
		for _, row := range resultMapList {
			language := windows.Language{}
			if val, ok := row["LanguageID"]; ok && val != nil {
				language.Code = val.(int64)
			}
			if val, ok := row["ShortLanguage"]; ok && val != nil {
				language.Name = val.(string)
			}
			if val, ok := row["LongLanguage"]; ok && val != nil {
				language.EnglishName = val.(string)
			}
			language.CreatedTime = time.Now().UnixMilli()
			language.UpdatedTime = time.Now().UnixMilli()
			code, _ := service.Repository.FindByLanguageCode(language.Code)
			if code.Id != 0 {
				continue
			}
			_, err := service.Repository.Create(&language)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}
	}
}

func (service *LanguageService) GetAllLanguage(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareQueryFromSearchFilter(filter, common.PatchLanguage.String(), true)
	var responsePage rest.ListResponseRest
	var packagePageList []windows.Language
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.PatchLanguage.String(), false)
		packagePageList, err = service.Repository.GetAllLanguage(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}
