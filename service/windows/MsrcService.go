package windows

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"regexp"
	"strings"
)

type MsrcService struct {
	ServerUrl  string
	ApiVersion string
}

func NewMsrcService() *MsrcService {
	return &MsrcService{
		ServerUrl:  "https://api.msrc.microsoft.com/cvrf/",
		ApiVersion: "2023",
	}
}

func (service MsrcService) GetKbDetailsFromSecurityUpdates(year, month string) ([]string, map[string]string) {
	kbIdMap := map[string]bool{}
	cveIdDetailsByKbId := map[string]string{}
	securityUpdateMap := service.GetSecurityUpdate(year, month)
	if securityUpdateMap != nil && len(securityUpdateMap) > 0 {
		if value, ok := securityUpdateMap["Vulnerability"]; ok {
			vulnerabilityList := value.([]interface{})
			if vulnerabilityList != nil && len(vulnerabilityList) > 0 {
				for _, vulnerability := range vulnerabilityList {
					vulnerabilityDetails := vulnerability.(map[string]interface{})
					if value, ok := vulnerabilityDetails["Remediations"]; ok {
						CVEId := ""
						if value, ok := vulnerabilityDetails["CVE"]; ok {
							CVEId = value.(string)
						}
						vulnerabilityRemediationList := value.([]interface{})
						if vulnerabilityRemediationList != nil && len(vulnerabilityRemediationList) > 0 {
							for _, vulnerabilityRemediation := range vulnerabilityRemediationList {
								vulnerabilityRemediationDetails := vulnerabilityRemediation.(map[string]interface{})
								if value, ok := vulnerabilityRemediationDetails["Description"]; ok {
									descriptionMap := value.(map[string]interface{})
									if desc, ok := descriptionMap["Value"]; ok {
										description := desc.(string)
										isMatched, _ := regexp.MatchString("^\\d+$", description)
										if isMatched {
											kbId := "KB" + description
											if value, ok := cveIdDetailsByKbId[kbId]; ok {
												if !strings.Contains(value, CVEId) {
													cveIdDetailsByKbId[kbId] = value + ", " + CVEId
												}
											} else {
												cveIdDetailsByKbId[kbId] = CVEId
											}
											kbIdMap[kbId] = true
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	return common.GetKeyList(kbIdMap), cveIdDetailsByKbId
}

func (service MsrcService) GetSecurityUpdate(year, month string) map[string]interface{} {
	var response map[string]interface{}
	url := fmt.Sprintf("%s%s-%s?api-version=%s", service.ServerUrl, year, month, service.ApiVersion)
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		logger.ServiceLogger.Error("[GetSecurityUpdate]", url, err.Error())
		return response
	}

	req.Header.Add("Accept", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		logger.ServiceLogger.Error("[GetSecurityUpdate]", url, err.Error())
		return response
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.ServiceLogger.Error("[GetSecurityUpdate]", url, err.Error())
		return response
	}

	err = json.Unmarshal(body, &response)
	if err != nil {
		logger.ServiceLogger.Error("[GetSecurityUpdate]", url, string(body), err.Error())
		return response
	}

	return response
}
