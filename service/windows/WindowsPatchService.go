package windows

import (
	"bufio"
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"github.com/docker/docker/pkg/fileutils"
	"io"
	"net/http"
	"os"
	"os/exec"
	"patch-central-repo/common"
	"patch-central-repo/constant"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	windows2 "patch-central-repo/model/windows"
	"patch-central-repo/repository/windows"
	"patch-central-repo/rest"
	service2 "patch-central-repo/service"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	UpdateCatalogUrl    = "https://www.catalog.update.microsoft.com/"
	DefenderUpdatesUrl  = "https://www.microsoft.com/en-us/wdsi/defenderupdates"
	DefinitionUpdateUrl = "https://definitionupdates.microsoft.com/download/DefinitionUpdates/VersionedSignatures/AM/%s/%s/%s/%s"
)

type WindowsPatchService struct {
	Repository *windows.WindowsPatchRepository
}

func NewWindowsPatchService() *WindowsPatchService {
	return &WindowsPatchService{
		Repository: windows.NewWindowsPatchRepository(),
	}
}

func (service *WindowsPatchService) SyncWindowsPatches() {
	logger.ServiceLogger.Debug("[SyncWindowsPatches] starting windows repository syncing...")
	logger.ServiceLogger.Debug("[SyncWindowsPatches] starting pool category repository syncing...")
	NewPatchCategoryService().PoolCategoryFromWSUS()
	logger.ServiceLogger.Debug("[SyncWindowsPatches] pool category repository syncing completed...")
	logger.ServiceLogger.Debug("[SyncWindowsPatches] starting language repository syncing...")
	NewLanguageService().SyncLanguageFromWsus()
	logger.ServiceLogger.Debug("[SyncWindowsPatches] language repository syncing completed...")
	logger.ServiceLogger.Debug("[SyncWindowsPatches] starting pool data from wsus syncing...")
	service.PoolDataFromWsus()
	logger.ServiceLogger.Debug("[SyncWindowsPatches] pool data from wsus syncing completed...")
	logger.ServiceLogger.Debug("[SyncWindowsPatches] starting windows patch msu file processing...")
	service.SyncWindowsPatchMsuFiles()
	logger.ServiceLogger.Debug("[SyncWindowsPatches] windows patch msu file processing completed...")
	logger.ServiceLogger.Debug("[SyncWindowsPatches] starting windows patch cve details sync from msrc...")
	service.SyncCVEDetailsFromMsrc()
	logger.ServiceLogger.Debug("[SyncWindowsPatches] windows patch cve details sync from msrc completed...")
	logger.ServiceLogger.Debug("[SyncWindowsPatches] windows repository syncing completed...")
}

func (service WindowsPatchService) PoolDataFromWsus() {
	wsusHistoryService := NewWsusSyncHistoryService()

	lastUpdatedRecoardTime := int64(0)
	wsusHistory, err := wsusHistoryService.Repository.FindFirstByOrderByUpdateTimeOfLastRecordDesc()
	if err == nil && wsusHistory.Id != 0 {
		lastUpdatedRecoardTime = wsusHistory.UpdateTimeOfLastRecord
		logger.ServiceLogger.Debug("Last updated record ", lastUpdatedRecoardTime)
	}

	engineVersion, _ := fetchLatestEngineVersion()

	logger.ServiceLogger.Info("Engine version: ", engineVersion)

	categoryCountMap := map[string]int64{}
	offset := int64(0)
	size := int64(500)
	for {
		var totalCount int64
		start := time.Now()
		totalCount, categoryCountMap = service.processKbData(offset, size, lastUpdatedRecoardTime, engineVersion, categoryCountMap)
		logger.ServiceLogger.Debug("processKbData time: ", time.Since(start))
		if totalCount == 0 {
			break
		} else {
			offset += size
		}
	}

	service.getMissingUUIDFromWsus(engineVersion, categoryCountMap)
	service.Create7ZXmlFolder()

}

func (service WindowsPatchService) Create7ZXmlFolder() {
	xmlDir := common.XMLDirectoryPath()
	entries, _ := os.ReadDir(xmlDir)
	for _, entry := range entries {
		if entry.IsDir() {
			categoryName := ""
			arch := ""
			if strings.Contains(entry.Name(), "_") {
				categoryName = strings.Split(entry.Name(), "_")[0]
				arch = strings.Split(entry.Name(), "_")[1]
			} else {
				categoryName = entry.Name()
			}
			if strings.Contains(strings.ToLower(arch), "x64") {
				arch = "amd64"
			} else {
				arch = "x86"
			}

			if strings.Contains(entry.Name(), "Microsoft 365 App") {
				categoryName = "%Microsoft 365 App%"
			}

			if categoryName != "" {
				folderName := entry.Name()
				if categoryName != "THIRD" {
					pchInfo := service.Repository.AllPatchListTxt(categoryName, "%"+arch+"%")
					if len(pchInfo) > 0 {
						if strings.Contains(entry.Name(), "Microsoft 365 App") {
							folderName = filepath.Join("Microsoft 365 Apps", "Office 2019", "Office LTSC_x64")
						}
						err := os.Remove(filepath.Join(xmlDir, folderName, "allpatchlist.txt"))
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						allpchfile, _ := os.Create(filepath.Join(xmlDir, folderName, "allpatchlist.txt"))
						writer := bufio.NewWriter(allpchfile)

						// Write each string to the file, followed by a newline
						for _, line := range pchInfo {
							_, err := writer.WriteString(line + "\n")
							if err != nil {
								logger.ServiceLogger.Error("Error writing to file:", err)
								err = os.Remove(filepath.Join(xmlDir, entry.Name(), "allpatchlist.txt"))
								if err != nil {
									logger.ServiceLogger.Error(err)
								}
								break
							}
						}
						err = writer.Flush()
						if err != nil {
							logger.ServiceLogger.Error("[Create7ZXmlFolder]", err.Error())
						}
						err = allpchfile.Close()
						if err != nil {
							logger.ServiceLogger.Error("[Create7ZXmlFolder]", err.Error())
						}
					} else {
						logger.ServiceLogger.Warn("patch info not found for category "+categoryName, " and arch ", arch)
					}
				}
				expectedFileName := strings.ReplaceAll(entry.Name(), " ", "") + ".7z"
				_, err := os.Stat(expectedFileName)
				if !os.IsNotExist(err) {
					err = os.Remove(expectedFileName)
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
				}
				common.CreateZipFile(filepath.Join(xmlDir, expectedFileName), filepath.Join(xmlDir, folderName, "*"))
			} else {
				logger.ServiceLogger.Warn("invalid category found for ", entry.Name())
			}
		}
	}
}

func (service WindowsPatchService) Create7ZCabFolder() {
	cabDir := common.CabDataDirectoryPath()
	entries, _ := os.ReadDir(cabDir)
	for _, entry := range entries {
		if entry.IsDir() {
			folderName := entry.Name()
			if service.isCabDirectoryUpdated(folderName) {
				expectedFileName := strings.ReplaceAll(folderName, " ", "") + ".7z"
				_, err := os.Stat(expectedFileName)
				if !os.IsNotExist(err) {
					err = os.Remove(expectedFileName)
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
				}
				common.CreateZipFile(filepath.Join(cabDir, expectedFileName), filepath.Join(cabDir, folderName, "*"))
			}
		}
	}
}

func (service WindowsPatchService) GetAllXmlFileName() []string {
	var xmlFileList []string
	xmlDir := common.XMLDirectoryPath()
	entries, _ := os.ReadDir(xmlDir)
	for _, entry := range entries {
		if !entry.IsDir() && strings.Contains(entry.Name(), ".7z") {
			xmlFileList = append(xmlFileList, entry.Name())
		}
	}

	xmlDir = common.ThirdPartyXMLDirectoryPath()
	entries, _ = os.ReadDir(xmlDir)
	for _, entry := range entries {
		if !entry.IsDir() && strings.Contains(entry.Name(), ".7z") {
			xmlFileList = append(xmlFileList, entry.Name())
		}
	}

	return xmlFileList
}

func (service WindowsPatchService) getMissingUUIDFromWsus(engineVersion string, categoryCountMap map[string]int64) {

	wsusService := NewWsusService()
	result, err := wsusService.RunQuery(VIEWS_PATCH_LIST_UUID)
	if err != nil {
		logger.ServiceLogger.Error("Error while executing VIEWS_PATCH_LIST_UUID :", err)
	}

	if len(result) > 0 {
		wsusUuidRepo := windows.NewWsusUUIDRepository()
		wsusUuidRepo.DeleteAll()
		wsusUuidRepo.ResetSequence()
		var wsusUuidList []windows2.WsusUUID
		for _, row := range result {
			if val, ok := row["UpdateId"]; ok {
				wsusUuidList = append(wsusUuidList, windows2.WsusUUID{UUID: val.(string)})
			}
		}
		wsusUuidRepo.SaveAll(wsusUuidList)

		missingKbResult, err := windows.NewWindowsPatchRepository().RunMissingKbFromWindowsPatchQuery()
		if err != nil {
			logger.ServiceLogger.Error("Error while executing GET_ALL_MISSING_KB_FROM_WINDOWS_PATH :", err)
		} else {
			var tempListOfUuid []string
			if len(missingKbResult) > 0 {
				for _, uuid := range missingKbResult {
					if uuid != "" {
						tempListOfUuid = append(tempListOfUuid, uuid)
					}
				}
				uuidChunks := common.PartitionStringList(tempListOfUuid, 100)
				for _, chunk := range uuidChunks {
					service.ProcessRemainingData(chunk, engineVersion, categoryCountMap)
				}
				wsusUuidRepo.DeleteAll()
				wsusUuidRepo.ResetSequence()
			}
		}
	}
}

func (service WindowsPatchService) ProcessRemainingData(chunk []string, engineVersion string, categoryCountMap map[string]int64) {

	query := fmt.Sprintf(GET_PATCH_LIST_QUERY_BY_UPDATE_ID_IN, "'"+strings.Join(chunk, "','")+"'")
	wsusService := NewWsusService()
	result, err := wsusService.RunQuery(query)
	if err != nil {
		logger.ServiceLogger.Error("Error while executing GET_PATCH_LIST_QUERY_BY_UPDATE_ID_IN :", err)
		return
	}

	lastUpdatedRecordTime := service.ProcessDataFromWSUS(result, engineVersion, categoryCountMap, 0)

	if lastUpdatedRecordTime > 0 && len(result) > 0 {
		wsusSyncHistory := windows2.WsusSyncHistory{
			ProcessedOn:            time.Now().UnixMilli(),
			WsusOffset:             0,
			WsusSize:               0,
			TotalSize:              int64(len(result)),
			UpdateTimeOfLastRecord: lastUpdatedRecordTime,
		}
		_, err := NewWsusSyncHistoryService().Repository.Create(&wsusSyncHistory)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
}

func (service WindowsPatchService) processKbData(offset, size, lastUpdatedRecoardTime int64, engineVersion string, categoryCountMap map[string]int64) (int64, map[string]int64) {
	if lastUpdatedRecoardTime == 0 {
		lastUpdatedRecoardTime = 1672511400000
	}
	lastUpdatedTime := time.Unix(0, lastUpdatedRecoardTime*int64(time.Millisecond))
	formattedTime := lastUpdatedTime.Format("2006-01-02 15:04:05.000")
	query := fmt.Sprintf(GET_PATCH_DETAILS_BY_LAST_UPDATED_DATE, formattedTime, offset, size)

	wsusService := NewWsusService()
	result, err := wsusService.RunQuery(query)
	if err != nil {
		logger.ServiceLogger.Error("Error while executing GET_PATCH_DETAILS_BY_LAST_UPDATED_DATE :", err, query)
		return 0, categoryCountMap
	}
	if result == nil {
		logger.ServiceLogger.Warn("no result found for GET_PATCH_DETAILS_BY_LAST_UPDATED_DATE :", query)
		return 0, categoryCountMap
	}

	start := time.Now()
	lastUpdatedRecoardTime = service.ProcessDataFromWSUS(result, engineVersion, categoryCountMap, lastUpdatedRecoardTime)
	logger.ServiceLogger.Debug("ProcessDataFromWSUS time: ", time.Since(start))

	if lastUpdatedRecoardTime > 0 && len(result) > 0 {
		wsusSyncHistory := windows2.WsusSyncHistory{
			ProcessedOn:            time.Now().UnixMilli(),
			WsusOffset:             offset,
			WsusSize:               size,
			TotalSize:              int64(len(result)),
			UpdateTimeOfLastRecord: lastUpdatedRecoardTime,
		}
		_, err = NewWsusSyncHistoryService().Repository.Create(&wsusSyncHistory)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	return 1, categoryCountMap
}

func (service WindowsPatchService) ProcessDataFromWSUS(result []map[string]interface{}, engineVersion string, categoryCountMap map[string]int64, lastUpdatedRecoardTime int64) int64 {
	var tempUUID []string
	wsusService := NewWsusService()
	patchMap := make(map[string]windows2.WindowsPatch)
	rowMap := make(map[string]map[string]interface{})
	var uuids []string
	for _, row := range result {
		uuid := row["UpdateId"].(string)
		revisionid := row["RevisionId"].(int64)
		lastupdatedtime := row["LastUpdatedTime"].(time.Time)
		wPatch, _ := service.Repository.FindByUUID(uuid)

		isUpdatedPatchReceived := wPatch.Id != 0 && lastupdatedtime.UnixMilli() != wPatch.LastUpdatedTime

		if wPatch.Id == 0 || wPatch.TempData || isUpdatedPatchReceived {
			rowMap[uuid] = row
			wPatch.UUID = uuid
			wPatch.TempData = false
			uuids = append(uuids, uuid)
			wPatch = PrepareWindowsPatchDetails(row, wPatch)
			affectedProductArch := GetAffectedProductByRevisionId(revisionid)
			if affectedProductArch != "" {
				wPatch.OsArch = affectedProductArch
			}
			patchMap[uuid] = wPatch
		}
	}

	start := time.Now()
	batchSize := len(uuids)
	for index := 0; index < len(uuids); index += batchSize {
		end := index + batchSize
		if end > len(uuids) {
			end = len(uuids)
		}
		uuidBatch := uuids[index:end]
		formattedBatch := "'" + strings.Join(uuidBatch, "','") + "'"
		logger.ServiceLogger.Debug(fmt.Sprintf(GET_PRODUCT_BY_UUID, formattedBatch))
		productDataList, err := wsusService.RunQuery(fmt.Sprintf(GET_PRODUCT_BY_UUID, formattedBatch))
		if err != nil {
			logger.ServiceLogger.Error("Error while executing GET_PRODUCT_BY_UUID :", err)
		} else {
			if productDataList != nil && len(productDataList) > 0 {
				pchCategoryService := NewPatchCategoryService()
				pchProductService := service2.NewPatchProductService()
				for _, productMap := range productDataList {
					if uuidArray, ok := productMap["uuid"]; ok && uuidArray != nil {
						uuid := uuidArray.(string)
						wPatch := patchMap[uuid]
						if productname, ok := productMap["productname"]; ok && productname != nil {
							var supportedProductUUId []string
							var supportedProduct []string
							if cattype, ok := productMap["cattype"]; ok && cattype != nil {
								if "Company" == cattype.(string) {
									wPatch.Company = productname.(string)
								} else if "ProductFamily" == cattype.(string) {
									wPatch.ProductFamily = productname.(string)
								} else if "Product" == cattype.(string) {
									if wPatch.Products != "" && !strings.Contains(wPatch.Products, productname.(string)) {
										wPatch.Products = wPatch.Products + "," + productname.(string)
									} else {
										wPatch.Products = productname.(string)
									}

									if val, ok := productMap["CategoryId"]; ok && val != nil {
										supportedProductUUId = append(supportedProductUUId, val.(string))
									}

									if wPatch.AffectedProduct == nil || len(wPatch.AffectedProduct) == 0 {
										if val, ok := productMap["CategoryId"]; ok && val != nil {
											categoryUUID := val.(string)
											pchCategory, _ := pchCategoryService.Repository.FindByUUID(categoryUUID)
											if pchCategory.Id != 0 {
												pchProductId := pchProductService.CreateOrGetProduct(pchCategory.Name, pchCategory.UUID, "", "")
												if pchProductId != 0 {
													supportedProduct = append(supportedProduct, categoryUUID)
												}
											}
										}
									}
								}
							}

							if supportedProduct != nil && len(supportedProduct) > 0 {
								wPatch.AffectedProduct = supportedProduct
							}

							if supportedProductUUId != nil && len(supportedProductUUId) > 0 {
								wPatch.ProductsUuid = supportedProductUUId
							}

							patchMap[uuid] = wPatch
						}
					}
				}
			}
		}
	}

	logger.ServiceLogger.Debug("forLoop time: ", time.Since(start))

	start = time.Now()
	for uuid, wPatch := range patchMap {
		row := rowMap[uuid]
		var tempUUIDList []string
		wPatch.SupersedesString, tempUUIDList = service.superSededPatchUuids(wPatch, row)
		tempUUIDList = common.RemoveValue(tempUUIDList, wPatch.UUID)

		tempUUID = append(tempUUID, tempUUIDList...)

		isLocallyPublished := false
		if val, ok := row["islocallypublished"]; ok && val != nil {
			isLocallyPublished = val.(bool)
		}

		fileDataList := getFileDetails(wPatch, row, isLocallyPublished, engineVersion)
		if fileDataList != nil && len(fileDataList) > 0 {
			wPatch.FileDetails = common.RemoveDuplicates(fileDataList)
		}

		if strings.Contains(strings.ToLower(wPatch.Title), "net framework") {
			if wPatch.FileDetails != nil {
				childKb := make(map[string]bool)
				hotfixPattern := regexp.MustCompile(`kb\d+`)
				for _, fileDetail := range wPatch.FileDetails {
					matches := hotfixPattern.FindStringSubmatch(strings.ToLower(fileDetail.FileName))
					if len(matches) > 0 {
						childKb[matches[0]] = true
					}
				}
				var childKbIDs []string
				for kbID := range childKb {
					childKbIDs = append(childKbIDs, kbID)
				}
				wPatch.KbIdToBeInstalled = strings.Join(childKbIDs, ",")
			}
		}

		arch := ""
		arch = GetArchitecture(wPatch.Title)
		if "" == arch {
			arch = strings.ToLower(wPatch.OsArch)
			if "" == arch {
				if wPatch.FileDetails != nil && len(wPatch.FileDetails) > 0 {
					var archList []string
					for _, fileDetail := range wPatch.FileDetails {
						archList = append(archList, GetArchitecture(fileDetail.FileName))
					}
					if archList != nil && len(archList) > 0 {
						arch = strings.Join(archList, ",")
					}
				}

				if arch != "" && (strings.Contains(wPatch.OsArch, "amd64") || strings.Contains(wPatch.OsArch, "AMD64")) {
					arch = strings.ReplaceAll(arch, "amd64", "x64")
					arch = strings.ReplaceAll(arch, "AMD64", "x64")
				}
			}
		}

		if arch != "" {
			wPatch.Arch = arch
		}

		if val, ok := categoryCountMap[wPatch.Classification]; ok {
			categoryCountMap[wPatch.Classification] = val + 1
		} else {
			categoryCountMap[wPatch.Classification] = 1
		}

		if constant.Contains(constant.AtLeastOneFileInstallationKBIds, wPatch.KbId) {
			wPatch.AtLeastOneFileInstallation = true
		}

		if wPatch.Id > 0 {
			_, err := service.Repository.Update(&wPatch)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		} else {
			id, err := service.Repository.Create(&wPatch)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
			wPatch.Id = id
		}

		if lastUpdatedRecoardTime < wPatch.LastUpdatedTime {
			lastUpdatedRecoardTime = wPatch.LastUpdatedTime
		}

		if !strings.Contains(wPatch.Classification, "Definition Updates") {
			go submitTaskToFetchXml(wPatch)
		}
		err := service.submitTaskToFetchCab(wPatch)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}

	logger.ServiceLogger.Debug("for Loop2 time: ", time.Since(start))

	if len(tempUUID) > 0 {
		go service.submitTaskForParsingUpdateCatalog(tempUUID)
	}

	return lastUpdatedRecoardTime
}

func (service WindowsPatchService) submitTaskForParsingUpdateCatalog(uuids []string) {
	if len(uuids) > 0 {
		for _, uuid := range uuids {
			kbId, _ := getKbIDByUUID(uuid)
			if kbId != "" {
				err := service.Repository.UpdateKbIDByUUID(uuid, kbId)
				if err != nil {
					logger.ServiceLogger.Error(err)
				}
			}
		}
	}
}

func getKbIDByUUID(uuid string) (string, error) {
	kbId := ""
	detailsDoc, err := getDetailsByUUID(uuid, nil)
	if err != nil {
		return "", err
	}
	patchInfo := detailsDoc.Find("#ScopedViewHandler_SoftwareInfo").Text()
	if patchInfo == "" {
		return "", nil
	}
	patchInfo = strings.ReplaceAll(patchInfo, " ", "")
	patchInfo = strings.ReplaceAll(patchInfo, "\n\n", "\n")
	patchInfo = strings.ReplaceAll(patchInfo, "\n\n", "\r")
	patchInfo = strings.ReplaceAll(patchInfo, "\n", "")
	patchInfo = strings.ReplaceAll(patchInfo, "\r", "\n")
	for _, info := range strings.Split(patchInfo, "\n") {
		if strings.Contains(info, "KBarticlenumbers:") {
			kbId = strings.Split(info, ":")[1]
		}
	}
	return kbId, nil
}

func getDetailsByUUID(uuid string, kbDetails interface{}) (*goquery.Document, error) {
	detailsURL := "https://www.catalog.update.microsoft.com/ScopedViewInline.aspx?updateid=" + uuid
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}
	response, err := client.Get(detailsURL)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(response.Body)

	body := response.Body
	detailsDoc, err := goquery.NewDocumentFromReader(body)
	if err != nil {
		return nil, err
	}

	if detailsDoc != nil && detailsDoc.Url != nil && strings.Contains(detailsDoc.Url.String(), "Error.aspx") {
		return getDetailsByUUID(uuid, kbDetails)
	}

	return detailsDoc, nil
}

func submitTaskToFetchXml(wPatch windows2.WindowsPatch) {
	if "" != wPatch.UUID {
		var osArchList []string
		if "" != wPatch.OsArch {
			detectoidNameList := strings.Split(wPatch.OsArch, ",")
			if detectoidNameList != nil && len(detectoidNameList) > 0 {
				for _, detectoidName := range detectoidNameList {
					architecture := GetArchitecture(detectoidName)
					if "" != architecture {
						osArchList = append(osArchList, architecture)
					}
				}
			}
		}
		if len(osArchList) == 0 {
			osArchList = append(osArchList, "x64")
			osArchList = append(osArchList, "x86")
		}

		if wPatch.ProductsUuid != nil && len(wPatch.ProductsUuid) > 0 {
			categoryService := NewPatchCategoryService()
			categoryList, _ := categoryService.Repository.FindByUUIDsIn(wPatch.ProductsUuid)
			if len(categoryList) > 0 {
				getXmlUpdatesAndStore(wPatch.UUID, categoryList, osArchList)
			}
		}
	}
}

func getXmlUpdatesAndStore(uuid string, categoryList []windows2.PatchCategory, osArchList []string) {
	xmlFolder := common.XMLDirectoryPath()
	if len(categoryList) > 0 {
		for _, category := range categoryList {
			if len(osArchList) > 0 {
				for _, arch := range osArchList {
					folderName := category.Name + "_" + arch
					folderName = filepath.Join(xmlFolder, folderName)
					_, err := os.Stat(folderName)
					if os.IsNotExist(err) {
						err = os.MkdirAll(folderName, 0755)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
					}

					filePath := filepath.Join(folderName, uuid+".xml")
					_, errr := os.Stat(filePath)

					if os.IsNotExist(errr) {
						xmlData, err := getXmlByUpdateId(uuid, filePath)
						if err != nil {
							continue
						}
						uuids := common.ParsePrerequisitesFromXmlData(xmlData)
						for _, nextUpdateId := range uuids {
							getXmlUpdatesAndStore(nextUpdateId, categoryList, osArchList)
						}
					}
				}
			}
		}
	}
}

func getXmlByUpdateId(uuid, filePath string) (string, error) {
	xmlData := ""
	resultMapList, err := NewWsusService().RunQuery(fmt.Sprintf(GET_XML_FOR_UPDATE_ID, uuid))
	if err == nil && len(resultMapList) > 0 {
		xmlDataMap := resultMapList[0]
		if len(xmlDataMap) > 0 {
			if val, ok := xmlDataMap["XmlSize"]; ok && (val == nil || val.(int64) == 0) {
				xmlData = xmlDataMap["RootElementXml"].(string)
			} else if val, ok := xmlDataMap["RootElementXmlCompressed"]; ok && val != nil {
				cabFilePath := filepath.Join(common.FileDirectoryPath(), "tmp", uuid)
				cabfile, err := os.Create(cabFilePath)
				if err != nil {
					logger.ServiceLogger.Error("Error creating cab file ", cabFilePath, " for uuid ", uuid)
					return "", err
				}

				_, err = cabfile.Write(val.([]byte))
				if err != nil {
					logger.ServiceLogger.Error("Error writing cab file ", cabFilePath, " for uuid ", uuid)
					return "", err
				}
				err = cabfile.Close()
				if err != nil {
					logger.ServiceLogger.Error(err)
				}

				fileToRead := "blob"
				command := "C:\\Program Files\\7-Zip\\7z.exe"
				platform := runtime.GOOS
				if platform != "windows" {
					command = "7z"
				}
				cmd := exec.Command(command, "e", cabFilePath, "-so", fileToRead)
				var stdout, stderr bytes.Buffer
				cmd.Stdout = &stdout
				cmd.Stderr = &stderr

				err = cmd.Run()
				if err != nil {
					logger.ServiceLogger.Error("Error extracting file: %v\n%s", err, stderr.String())
					return "", err
				}

				tempDataBytes, err := io.ReadAll(&stdout)
				if err != nil {
					logger.ServiceLogger.Error("Error reading file content: %v", err)
					return "", err
				}

				dataBytes := removeNullbyteFormByteArray(tempDataBytes)
				xmlData = string(dataBytes)
				defer func(name string) {
					err = os.Remove(name)
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
				}(cabFilePath)
			}
		}
	}

	if len(xmlData) > 0 {

		if strings.Contains(xmlData, "upd:LocalizedPropertiesCollection") {
			tempXmlData := xmlData
			tempXmlData = strings.ReplaceAll(tempXmlData, "<upd:LocalizedPropertiesCollection", "$$--##--TEMP--##--$$")
			tempXmlData = strings.ReplaceAll(tempXmlData, "</upd:LocalizedPropertiesCollection>", "$$--##--TEMP--##--$$")
			xmlDataList := strings.Split(tempXmlData, "$$--##--TEMP--##--$$")
			if len(xmlDataList) == 3 {
				xmlData = xmlDataList[0] + xmlDataList[2]
			}
		}

		xmlFile, err := os.Create(filePath)
		if err != nil {
			logger.ServiceLogger.Error("Error creating xml file ", filePath, " for uuid ", uuid)
			return "", err
		}
		defer func(xmlFile *os.File) {
			err = xmlFile.Close()
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}(xmlFile)

		_, err = xmlFile.Write([]byte(xmlData))
		if err != nil {
			logger.ServiceLogger.Error("Error writing xml file ", filePath, " for uuid ", uuid)
			return "", err
		}
	}

	return xmlData, err
}

func (service WindowsPatchService) submitTaskToFetchCab(wPatch windows2.WindowsPatch) error {
	if wPatch.Id > 0 {
		uuid := wPatch.UUID
		releaseDate := time.UnixMilli(wPatch.ReleaseDate)
		dateDirName := releaseDate.Format("2006-01")
		dateDir := filepath.Join(common.CabDataDirectoryPath(), dateDirName)
		_, err := os.Stat(dateDir)
		if os.IsNotExist(err) {
			err := os.MkdirAll(dateDir, 0755)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}
		file7z := filepath.Join(dateDir, uuid+".7z")
		_, err = os.Stat(file7z)
		if os.IsNotExist(err) {
			fileDetails := wPatch.FileDetails
			if len(fileDetails) > 0 {
				var downloadUrls []string
				for _, fileData := range fileDetails {
					if strings.HasSuffix(strings.ToLower(fileData.DownloadUrl), ".msu") && fileData.FetchFromMSU {
						downloadUrls = append(downloadUrls, fileData.DownloadUrl)
					}
				}
				if len(downloadUrls) > 0 {
					var fileDownloaded bool
					pchCabDir := filepath.Join(dateDir, uuid)
					_, err := os.Stat(pchCabDir)
					if os.IsNotExist(err) {
						err = os.MkdirAll(pchCabDir, 0755)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
					}
					for i, downloadUrl := range downloadUrls {
						pchCabTempDir := filepath.Join(dateDir, uuid, "tmp")
						_, err := os.Stat(pchCabTempDir)
						if os.IsNotExist(err) {
							err = os.MkdirAll(pchCabTempDir, 0755)
							if err != nil {
								logger.ServiceLogger.Error(err)
							}
						}
						msuFilePath := filepath.Join(common.CabTempDirectoryPath(), uuid+".msu")
						if ExecuteRequestToDownloadFile(downloadUrl, msuFilePath, map[string]string{}) {
							logger.ServiceLogger.Info("downloaded msu file " + downloadUrl + " for uuid " + uuid)
							args := []string{"x", msuFilePath, "-o" + pchCabTempDir}
							command := "C:\\Program Files\\7-Zip\\7z.exe"
							platform := runtime.GOOS
							if platform != "windows" {
								command = "7z"
							}
							cmd := exec.Command(command, args...)
							var stdout, stderr bytes.Buffer
							cmd.Stdout = &stdout
							cmd.Stderr = &stderr

							err = cmd.Run()
							if err != nil {
								logger.ServiceLogger.Error("Error extracting file: %v\n%s", err, stderr.String())
								return err
							}
							pchCabTempWsusCab := filepath.Join(dateDir, uuid, "tmp", "WSUSSCAN.cab")
							_, errr := os.Stat(pchCabTempWsusCab)
							if !os.IsNotExist(errr) {
								fileDownloaded = true
								_, err = fileutils.CopyFile(pchCabTempWsusCab, filepath.Join(pchCabDir, "wsusscan-"+fmt.Sprint(i)+".cab"))
								if err != nil {
									logger.ServiceLogger.Error(err)
								}
							} else {
								pchCabTempWsusCab = filepath.Join(dateDir, uuid, "tmp", "wsusscan.cab")
								_, errr = os.Stat(pchCabTempWsusCab)
								if !os.IsNotExist(errr) {
									fileDownloaded = true
									_, err = fileutils.CopyFile(pchCabTempWsusCab, filepath.Join(pchCabDir, "wsusscan-"+fmt.Sprint(i)+".cab"))
									if err != nil {
										logger.ServiceLogger.Error(err)
									}
								}
							}
							err = os.Remove(msuFilePath)
							if err != nil {
								logger.ServiceLogger.Error(err)
							}
							err = os.RemoveAll(pchCabTempDir)
							if err != nil {
								logger.ServiceLogger.Error(err)
							}
						} else {
							logger.ServiceLogger.Error("failed to download msu file " + downloadUrl + " for uuid " + uuid)
						}
					}
					err = os.Remove(filepath.Join(dateDir, uuid+".7z"))
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
					if fileDownloaded {
						common.CreateZipFile(filepath.Join(dateDir, uuid+".7z"), filepath.Join(dateDir, uuid, "*"))
						err = os.RemoveAll(pchCabDir)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						wPatch.CabExist = true
						wPatch.UpdatedTime = time.Now().UnixMilli()
						_, err := service.Repository.Update(&wPatch)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						service.updateCabSyncHistory(dateDirName)
						return nil
					} else {
						err = os.RemoveAll(pchCabDir)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						return errors.New("failed to download cab file")
					}
				} else {
					return errors.New("patch msu file details not found")
				}
			} else {
				return errors.New("patch file details not found")
			}

		} else {
			wPatch.CabExist = true
			wPatch.UpdatedTime = time.Now().UnixMilli()
			_, err := service.Repository.Update(&wPatch)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
			service.updateCabSyncHistory(dateDirName)
		}
	}
	return errors.New("patch not found")
}

func (service WindowsPatchService) updateCabSyncHistory(releaseDate string) {
	cabSyncHistoryService := NewCabSyncHistoryService()
	cabSyncHistory := windows2.CabSyncHistory{
		LastSyncTime: time.Now().UnixMilli(),
		ReleaseDate:  releaseDate,
		Compress:     true,
	}
	history, _ := cabSyncHistoryService.Repository.GetByReleaseDate(releaseDate)
	if history.Id != 0 {
		cabSyncHistory.Id = history.Id
		_, err := cabSyncHistoryService.Repository.Update(&cabSyncHistory)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	} else {
		_, err := cabSyncHistoryService.Repository.Create(&cabSyncHistory)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
}

func (service WindowsPatchService) isCabDirectoryUpdated(releaseDate string) bool {
	cabSyncHistoryService := NewCabSyncHistoryService()
	history, _ := cabSyncHistoryService.Repository.GetByReleaseDate(releaseDate)
	if history.Id != 0 {
		result := history.Compress
		if history.Compress {
			history.Compress = false
			_, err := cabSyncHistoryService.Repository.Update(&history)
			if err != nil {
				logger.ServiceLogger.Error(err)
			}
		}
		return result
	}
	return false
}

func GetCabByUpdateId(uuid string) (string, error) {
	pch, err := NewWindowsPatchService().Repository.GetByUUID(uuid)
	if err == nil && pch.Id > 0 {
		file7z := filepath.Join(common.CabDataDirectoryPath(), uuid+".7z")
		_, errrr := os.Stat(file7z)
		if os.IsNotExist(errrr) {
			fileDetails := pch.FileDetails
			if len(fileDetails) > 0 {
				var downloadUrls []string
				for _, fileData := range fileDetails {
					if strings.HasSuffix(strings.ToLower(fileData.DownloadUrl), ".msu") {
						downloadUrls = append(downloadUrls, fileData.DownloadUrl)
					}
				}
				if len(downloadUrls) > 0 {
					var fileDownloaded bool
					pchCabDir := filepath.Join(common.CabDataDirectoryPath(), uuid)
					_, err := os.Stat(pchCabDir)
					if os.IsNotExist(err) {
						err = os.MkdirAll(pchCabDir, 0755)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
					}
					for i, downloadUrl := range downloadUrls {
						pchCabTempDir := filepath.Join(common.CabDataDirectoryPath(), uuid, "tmp")
						_, err := os.Stat(pchCabTempDir)
						if os.IsNotExist(err) {
							err = os.MkdirAll(pchCabTempDir, 0755)
							if err != nil {
								logger.ServiceLogger.Error(err)
							}
						}
						msuFilePath := filepath.Join(common.CabTempDirectoryPath(), uuid+".msu")
						if ExecuteRequestToDownloadFile(downloadUrl, msuFilePath, map[string]string{}) {
							args := []string{"x", msuFilePath, "-o" + pchCabTempDir}
							command := "C:\\Program Files\\7-Zip\\7z.exe"
							platform := runtime.GOOS
							if platform != "windows" {
								command = "7z"
							}
							cmd := exec.Command(command, args...)
							var stdout, stderr bytes.Buffer
							cmd.Stdout = &stdout
							cmd.Stderr = &stderr

							err = cmd.Run()
							if err != nil {
								logger.ServiceLogger.Error("Error extracting file: %v\n%s", err, stderr.String())
								return "", err
							}
							pchCabTempWsusCab := filepath.Join(common.CabDataDirectoryPath(), uuid, "tmp", "WSUSSCAN.cab")
							_, errr := os.Stat(pchCabTempWsusCab)
							if !os.IsNotExist(errr) {
								fileDownloaded = true
								_, err = fileutils.CopyFile(pchCabTempWsusCab, filepath.Join(pchCabDir, "wsusscan-"+fmt.Sprint(i)+".cab"))
								if err != nil {
									logger.ServiceLogger.Error(err)
								}
							} else {
								pchCabTempWsusCab = filepath.Join(common.CabDataDirectoryPath(), uuid, "tmp", "wsusscan.cab")
								_, errr = os.Stat(pchCabTempWsusCab)
								if !os.IsNotExist(errr) {
									fileDownloaded = true
									_, err = fileutils.CopyFile(pchCabTempWsusCab, filepath.Join(pchCabDir, "wsusscan-"+fmt.Sprint(i)+".cab"))
									if err != nil {
										logger.ServiceLogger.Error(err)
									}
								}
							}
							err = os.Remove(msuFilePath)
							if err != nil {
								logger.ServiceLogger.Error(err)
							}
							err = os.RemoveAll(pchCabTempDir)
							if err != nil {
								logger.ServiceLogger.Error(err)
							}
						}
					}
					err = os.Remove(filepath.Join(common.CabDataDirectoryPath(), uuid+".7z"))
					if err != nil {
						logger.ServiceLogger.Error(err)
					}
					if fileDownloaded {
						//args := []string{"a", "-t7z", filepath.Join(common.CabDataDirectoryPath(), uuid+".7z"), filepath.Join(common.CabDataDirectoryPath(), uuid, "*")}
						//exec.Command(`C:\Program Files\7-Zip\7z.exe`, args...).Run()
						common.CreateZipFile(filepath.Join(common.CabDataDirectoryPath(), uuid+".7z"), filepath.Join(common.CabDataDirectoryPath(), uuid, "*"))
						err = os.RemoveAll(pchCabDir)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						return filepath.Join(common.CabDataDirectoryPath(), uuid+".7z"), nil
					} else {
						err = os.RemoveAll(pchCabDir)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						return "", errors.New("failed to download cab file")
					}
				} else {
					return "", errors.New("patch msu file details not found")
				}
			} else {
				return "", errors.New("patch file details not found")
			}
		}
	}
	return "", errors.New("patch not found")
}

func ExecuteRequestToDownloadFile(url, downloadFilePath string, headers map[string]string) bool {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}
	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return false
	}
	if headers != nil && len(headers) > 0 {
		for key, value := range headers {
			request.Header.Add(key, value)
		}
	}

	resp, err := client.Do(request)
	if err != nil {
		logger.ServiceLogger.Error(err.Error())
		return false
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err.Error())
		}
	}(resp.Body)

	_, err2 := os.Stat(downloadFilePath)
	if !os.IsNotExist(err2) {
		err = os.Remove(downloadFilePath)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	out, err := os.Create(downloadFilePath)
	if err != nil {
		return false
	}

	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return false
	}

	defer func(out *os.File) {
		err = out.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(out)

	return true
}

func removeNullbyteFormByteArray(input []byte) []byte {
	resultLen := (len(input) + 1) / 2

	result := make([]byte, resultLen)

	for i, j := 0, 0; i < len(input); i += 2 {
		result[j] = input[i]
		j++
	}

	return result
}

func GetArchitecture(title string) string {
	name := strings.ToLower(title)
	var arch string

	if strings.Contains(name, "x64") || strings.Contains(name, "64-bit") || strings.Contains(name, "amd64") {
		arch = "x64"
	} else if strings.ContainsAny(name, "x86") || strings.ContainsAny(name, "32-bit") {
		arch = "x86"
	} else if strings.ContainsAny(name, "arm64") {
		arch = "ARM64"
	} else if strings.ContainsAny(name, "itanium") || strings.ContainsAny(name, "ia64") {
		arch = "IA64"
	} else if strings.Contains(name, "arm") {
		arch = "ARM"
	}

	return arch
}

func getFileDetails(wPatch windows2.WindowsPatch, row map[string]interface{}, isLocallyPublished bool, engineVersion string) []model.FileData {
	var fileDataList []model.FileData
	if strings.HasPrefix(wPatch.Title, "Security Intelligence Update for Microsoft Defender Antivirus") || strings.HasPrefix(wPatch.Title, "Security Intelligence Update for Windows Defender Antivirus") {
		fileDataSet := buildDefenderUrlSet(wPatch.Title, engineVersion)
		if fileDataSet != nil && len(fileDataSet) > 0 {
			fileDataList = append(fileDataList, fileDataSet...)
		}
	} else {
		downloadURLs := getDownloadUrlsByWindowsPatchUUID(wPatch.UUID)
		if downloadURLs != nil && len(downloadURLs) > 0 {
			for _, url := range downloadURLs {
				fileName := url[strings.LastIndex(url, "/")+1:]
				fileSize := common.GetFileSizeFromUrl(url)
				fileDataList = append(fileDataList, model.FileData{
					FileName:     fileName,
					DownloadUrl:  url,
					Size:         fileSize,
					FetchFromMSU: true,
				})
			}
		} else if val, ok := row["BundledRevisionID"]; ok && val != nil && "" != val.(string) {
			wsusService := NewWsusService()
			bundleRevisionIds := strings.Split(val.(string), ",")
			for _, bundleRevisionId := range bundleRevisionIds {
				fileDetailsList, _ := wsusService.RunQuery(fmt.Sprintf(GET_FILE_DETAILS_BY_REVISION_ID, bundleRevisionId))
				if fileDetailsList != nil && len(fileDetailsList) > 0 {
					for _, fileDetails := range fileDetailsList {
						fileData := model.FileData{}
						if val, ok := fileDetails["lang"]; ok && val != nil {
							fileData.Language = val.(int64)
						}

						if val, ok := fileDetails["filename"]; ok && val != nil {
							fileData.FileName = val.(string)
						}

						if val, ok := fileDetails["url"]; ok && val != nil {
							fileData.DownloadUrl = val.(string)
						}

						if val, ok := fileDetails["fsize"]; ok && val != nil {
							fileData.Size = val.(int64)
						}

						isFileDataExist := false
						for _, data := range fileDataList {
							if data.DownloadUrl == fileData.DownloadUrl && data.Language == fileData.Language {
								data = fileData
								isFileDataExist = true
							}
						}
						fileData.FetchFromMSU = false
						if !isFileDataExist {
							fileDataList = append(fileDataList, fileData)
						}
					}
				}
			}
		}

		if isLocallyPublished && len(fileDataList) == 0 {
			// TODO handle xml scan and fetch file data
			// wpatch serv 463
		}
	}

	return fileDataList
}

func getDownloadUrlsByWindowsPatchUUID(uuid string) []string {
	var downloadUrls []string
	data := fmt.Sprintf("[{\"size\":0,\"languages\":\"\",\"uidInfo\":\"%s\",\"updateID\":\"%s\"}]", uuid, uuid)
	resp, err := http.Post(UpdateCatalogUrl+"DownloadDialog.aspx", "application/x-www-form-urlencoded;charset=UTF-8", strings.NewReader("updateIDs="+data))
	if err != nil {
		logger.ServiceLogger.Error("Error while getting download URLs ", err)
		return downloadUrls
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.ServiceLogger.Error("Error reading response body ", err)
		return downloadUrls
	}
	scriptTags := strings.Split(string(body), "<script")
	for _, tag := range scriptTags {
		re := regexp.MustCompile("(downloadInformation)\\[\\d+\\]\\.files\\[\\d+\\].url = \\'[(http(s)?):\\/\\/(www\\.)?a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)\\'")
		matches := re.FindStringSubmatch(tag)
		if len(matches) > 1 {
			url := matches[0]
			url = strings.Split(url, "=")[1]
			url = strings.Trim(url, " ")
			url = strings.Trim(url, "'")
			downloadUrls = append(downloadUrls, url)
		}
	}

	return downloadUrls
}

func buildDefenderUrlSet(title string, engineVersion string) []model.FileData {
	var fileDataList []model.FileData
	if "" != title {
		version := ""
		re := regexp.MustCompile(`\((.*?)\)`)
		matches := re.FindStringSubmatch(title)
		if len(matches) > 1 {
			version := matches[1]
			if strings.Contains(version, "Version") {
				version = strings.ReplaceAll(version, "Version", "")
				version = strings.TrimSpace(version)
			}
		}

		if version != "" {

			// Win7 file name set
			for _, arch := range []string{"x64", "amd64"} {
				fileName := fmt.Sprintf("mpas-fe_%s.exe", arch)
				url := fmt.Sprintf(DefinitionUpdateUrl, version, engineVersion, arch, "mpas-fe.exe")
				fileSize := common.GetFileSizeFromUrl(url)
				fileDataList = append(fileDataList, model.FileData{
					FileName:     fileName,
					DownloadUrl:  url,
					Size:         fileSize,
					FetchFromMSU: false,
				})
			}

			// Win10 file name set
			for _, arch := range []string{"x64", "amd64", "arm"} {
				fileName := fmt.Sprintf("mpam-fe_%s.exe", arch)
				url := fmt.Sprintf(DefinitionUpdateUrl, version, engineVersion, arch, "mpam-fe.exe")
				fileSize := common.GetFileSizeFromUrl(url)
				fileDataList = append(fileDataList, model.FileData{
					FileName:     fileName,
					DownloadUrl:  url,
					Size:         fileSize,
					FetchFromMSU: false,
				})
			}
		}
	}
	return fileDataList
}

func (service WindowsPatchService) superSededPatchUuids(patch windows2.WindowsPatch, row map[string]interface{}) (string, []string) {
	var tempUUIDList []string
	var supersededUUIDList []string
	supersededUUID := ""
	if val, ok := row["Superseded"]; ok && val != nil {
		var supersededPchList []map[string]interface{}
		err := json.Unmarshal([]byte(val.(string)), &supersededPchList)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		if supersededPchList != nil && len(supersededPchList) > 0 {
			for _, supersededPchDetails := range supersededPchList {
				if val, ok := supersededPchDetails["SupersededUpdateID"]; ok && val != nil {
					pch, _ := service.Repository.FindByUUID(val.(string))
					if pch.Id == 0 {
						pch := windows2.WindowsPatch{
							UUID:          val.(string),
							TempData:      true,
							ProductFamily: patch.ProductFamily,
							Products:      patch.Products,
						}
						_, err = service.Repository.Create(&pch)
						if err != nil {
							logger.ServiceLogger.Error(err)
						}
						tempUUIDList = append(tempUUIDList, val.(string))
					}
					supersededUUIDList = append(supersededUUIDList, val.(string))
				}
			}
			supersededUUID = strings.Join(supersededUUIDList, ",")
		}
	}
	return supersededUUID, tempUUIDList
}

func GetAffectedProductByRevisionId(revisionid int64) string {
	archBaseOid := ""
	query := fmt.Sprintf(GET_AFFECTED_PRODUCT_BY_REVISION_ID, revisionid)
	result, err := NewWsusService().RunQuery(query)
	if err == nil && result != nil && len(result) > 0 {
		for _, row := range result {
			if val, ok := row["DetectoidType"]; ok && val != nil && "Architecture" == val.(string) {
				if val, ok := row["Title"]; ok && val != nil {
					if archBaseOid != "" {
						archBaseOid += "," + val.(string)
					} else {
						archBaseOid = val.(string)
					}
				}
			}
		}
	}
	return archBaseOid
}

func PrepareWindowsPatchDetails(row map[string]interface{}, wPatch windows2.WindowsPatch) windows2.WindowsPatch {
	if val, ok := row["DefaultTitle"]; ok && val != nil {
		wPatch.Title = val.(string)
	}
	if val, ok := row["RevisionNumber"]; ok && val != nil {
		wPatch.RevisionId = val.(int64)
	}
	if val, ok := row["DefaultDescription"]; ok && val != nil {
		wPatch.Description = val.(string)
	}
	if val, ok := row["Category"]; ok && val != nil {
		wPatch.Classification = val.(string)
	}
	if val, ok := row["CreationDate"]; ok && val != nil {
		wPatch.ReleaseDate = val.(time.Time).UnixMilli()
	}
	if val, ok := row["LastUpdatedTime"]; ok && val != nil {
		wPatch.LastUpdatedTime = val.(time.Time).UnixMilli()
		wPatch.WsusLastUpdatedTime = val.(time.Time).UnixMilli()
	}
	if val, ok := row["IsLeaf"]; ok && val != nil {
		wPatch.Leaf = val.(bool)
	}
	if val, ok := row["IsDeclined"]; ok && val != nil {
		wPatch.Declined = val.(bool)
	}
	if val, ok := row["CanUninstall"]; ok && val != nil {
		wPatch.CanUninstall = val.(bool)
	}
	if val, ok := row["MsrcSeverity"]; ok && val != nil {
		wPatch.Severity = val.(string)
	}
	if val, ok := row["PublicationState"]; ok && val != nil {
		wPatch.PublishState = val.(string)
	}
	if val, ok := row["UpdateType"]; ok && val != nil {
		wPatch.UpdateType = val.(string)
	}
	if val, ok := row["KnowledgebaseArticle"]; ok && val != nil {
		wPatch.KbId = val.(string)
	}
	if val, ok := row["SecurityBulletin"]; ok && val != nil {
		wPatch.BulletinId = val.(string)
	}
	if val, ok := row["InstallationImpact"]; ok && val != nil {
		wPatch.InstallationImpact = val.(string)
	}
	if val, ok := row["RestartBehaviour"]; ok && val != nil {
		wPatch.RestartBehaviour = val.(string)
	}
	if val, ok := row["SupportedLanguage"]; ok && val != nil {
		wPatch.SupportedLanguage = val.(string)
	}
	if val, ok := row["MoreInfoUrl"]; ok && val != nil {
		wPatch.MoreInfoUrl = val.(string)
	}
	if val, ok := row["InstallationCanRequestUserInput"]; ok && val != nil {
		wPatch.InstallationCanRequestUserInput = val.(bool)
	}
	if val, ok := row["InstallationRequiresNetworkConnectivity"]; ok && val != nil {
		wPatch.InstallationRequiresNetworkConnectivity = val.(bool)
	}
	wPatch.CreatedTime = time.Now().UnixMilli()
	wPatch.UpdatedTime = time.Now().UnixMilli()

	return wPatch
}

func fetchLatestEngineVersion() (string, error) {
	engineVersion := ""
	cmd := exec.Command("curl", "--location", DefenderUpdatesUrl)
	body, err := cmd.Output()
	if err != nil {
		logger.ServiceLogger.Error("Error running command:", err)
		return engineVersion, err
	}
	output := string(body)

	for _, line := range strings.Split(output, "\n") {
		if strings.Contains(line, "Engine Version:") {
			versionDetails := strings.Split(line, ":")[1]
			versionDetails = strings.ReplaceAll(versionDetails, "<span>", "")
			versionDetails = strings.ReplaceAll(versionDetails, "</span>", "")
			versionDetails = strings.ReplaceAll(versionDetails, "</li>", "")
			versionDetails = strings.ReplaceAll(versionDetails, "\r", "")
			versionDetails = strings.Trim(versionDetails, " ")
			return versionDetails, nil
		}
	}

	return engineVersion, nil
}

func (service WindowsPatchService) SyncWindowsDotNetPatchFromUpdateCatalog() {
	query := `("Framework")-"Update for"-"Security"-"intel"-"Quality"-"Cumulative"`
	NewUpdateCatalogService().ProcessUpdateCatalogDataByQueryList([]string{query})
}

func (service WindowsPatchService) SyncWindowsPatchMsuFiles() {
	uuidList, err := service.Repository.GetAllUuidByCreatedTime(time.Now().AddDate(0, 0, -30).UnixMilli())
	if err != nil {
		return
	}

	if len(uuidList) > 0 {
		for _, uuid := range uuidList {
			service.processMsuFileData(uuid)
		}
	}

	service.Create7ZCabFolder()
}

func (service WindowsPatchService) processMsuFileData(uuid string) {
	patch, _ := service.Repository.FindByUUID(uuid)

	if patch.Id != 0 {
		var fileDataList []model.FileData
		downloadURLs := getDownloadUrlsByWindowsPatchUUID(uuid)
		if downloadURLs != nil && len(downloadURLs) > 0 {
			for _, url := range downloadURLs {
				fileName := url[strings.LastIndex(url, "/")+1:]
				fileSize := common.GetFileSizeFromUrl(url)
				fileDataList = append(fileDataList, model.FileData{
					FileName:     fileName,
					DownloadUrl:  url,
					Size:         fileSize,
					FetchFromMSU: true,
				})
			}
		}

		if len(fileDataList) > 0 {
			var finalFileDetails []model.FileData
			for _, detail := range patch.FileDetails {
				isFileFound := false
				for _, data := range fileDataList {
					if data.DownloadUrl == detail.DownloadUrl {
						isFileFound = true
						break
					}
				}
				if !isFileFound {
					detail.FetchFromMSU = false
					finalFileDetails = append(finalFileDetails, detail)
				}
			}
			finalFileDetails = append(finalFileDetails, fileDataList...)
			if len(finalFileDetails) > 0 {
				finalFileDetails = append(finalFileDetails, patch.FileDetails...)
				patch.FileDetails = common.RemoveDuplicates(finalFileDetails)
				patch.UpdatedTime = time.Now().UnixMilli()
				_, err := service.Repository.Update(&patch)
				if err != nil {
					logger.ServiceLogger.Error(err)
				}

				err = service.submitTaskToFetchCab(patch)
				if err != nil {
					logger.ServiceLogger.Error(err)
				}
			}
		}
	}
}

func (service WindowsPatchService) processFileData(uuid string) {
	patch, _ := service.Repository.FindByUUID(uuid)
	if patch.Id == 0 {
		cmd := exec.Command("powershell.exe", "(Get-WSUSServer).ImportUpdateFromCatalogSite('"+uuid+"', @())")
		var stdout, stderr bytes.Buffer
		cmd.Stdout = &stdout
		cmd.Stderr = &stderr

		err := cmd.Run()
		if err != nil {
			logger.ServiceLogger.Error("Error extracting file: %v\n%s", err, stderr.String())

		}

		tempDataBytes, err := io.ReadAll(&stdout)
		if err != nil {
			logger.ServiceLogger.Error("Error reading file content: %v", err)
		}

		if len(tempDataBytes) > 0 {
			logger.ServiceLogger.Debug("Result : ", string(tempDataBytes))
		}

		if err != nil {
			logger.ServiceLogger.Error(err)
		}

	}
}

func (service WindowsPatchService) SyncCVEDetailsFromMsrc() {
	currentDir := common.CurrentWorkingDir()
	now := time.Now().UTC()

	// Start from the previous month's 1st day at midnight
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC).AddDate(0, -1, 0)

	// If RESYNC_CVE is true, reset to a fixed past date
	reSync, _ := strconv.ParseBool(common.GetEnv("RESYNC_CVE", "false"))
	if reSync {
		startDate = time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)
	}

	for date := startDate; date.Before(now); date = date.AddDate(0, 1, 0) {
		year := strconv.Itoa(date.Year())
		monthShort := date.Month().String()[:3] // e.g., "Jan", "Feb"
		logger.ServiceLogger.Debug("SyncCVEDetailsFromMsrc Process started for month and year ", monthShort, "-", year)
		_, cveDetails := NewMsrcService().GetKbDetailsFromSecurityUpdates(year, monthShort)
		if len(cveDetails) > 0 {
			for kbId, cveDetail := range cveDetails {
				kbNumber := strings.ReplaceAll(kbId, "KB", "")
				wPatchList, _ := service.GetAllWindowsPatch(rest.SearchFilter{
					Qualification: []rest.Qualification{
						rest.BuildQualification("kb_id", "equals", strings.TrimSpace(kbNumber), "and"),
					},
				})
				if wPatchList.TotalCount > 0 {
					for _, wPatch := range wPatchList.ObjectList.([]windows2.WindowsPatch) {
						if wPatch.Id != 0 {
							if wPatch.CveNumber == "" {
								wPatch.CveNumber = cveDetail
								wPatch.LastUpdatedTime = time.Now().UnixMilli()
								wPatch.UpdatedTime = time.Now().UnixMilli()
								_, err := service.Repository.Update(&wPatch)
								if err != nil {
									logger.ServiceLogger.Error(err)
								}
							} else {
								cveIds := map[string]bool{}
								tempCveIds := strings.Split(wPatch.CveNumber, ",")
								for _, tempCveId := range tempCveIds {
									cveIds[strings.TrimSpace(tempCveId)] = true
								}
								tempCveDetails := strings.Split(cveDetail, ",")
								for _, tempCveId := range tempCveDetails {
									cveIds[strings.TrimSpace(tempCveId)] = true
								}
								wPatch.CveNumber = strings.Join(common.GetKeyList(cveIds), ", ")
								wPatch.LastUpdatedTime = time.Now().UnixMilli()
								wPatch.UpdatedTime = time.Now().UnixMilli()
								_, err := service.Repository.Update(&wPatch)
								if err != nil {
									logger.ServiceLogger.Error(err)
								}
							}
							logger.ServiceLogger.Debug("cve : ", cveDetail)
							logger.ServiceLogger.Debug("kb : ", kbId)
						}
					}
				}
			}
		}
	}
	err := exec.Command("cmd", "/C", "sed", "-i", `s/RESYNC_CVE="true"/RESYNC_CVE="false"/`, filepath.Join(currentDir, "app.config")).Run()
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
}

func (service WindowsPatchService) SyncWindowsPatchFromUpdateCatalog() {
	now := time.Now().UTC()

	// Start from the previous month's 1st day at midnight
	startDate := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC).AddDate(0, -1, 0)

	// If RESYNC_UPDATECATALOG is true, reset to a fixed past date
	reSync, _ := strconv.ParseBool(common.GetEnv("RESYNC_UPDATECATALOG", "false"))
	if reSync {
		startDate = time.Date(2023, 6, 1, 0, 0, 0, 0, time.UTC)
	}

	for date := startDate; date.Before(now); date = date.AddDate(0, 1, 0) {
		var queryList []string

		year := strconv.Itoa(date.Year())
		monthShort := date.Month().String()[:3]               // e.g., "Jan", "Feb"
		monthPadded := fmt.Sprintf("%02d", int(date.Month())) // e.g., "01", "12"

		logger.ServiceLogger.Debug(fmt.Sprintf("\n Update catalog sync : year - %v , month - %v", year, monthShort))

		kbIdList, _ := NewMsrcService().GetKbDetailsFromSecurityUpdates(year, monthShort)

		catalogSearchQuery := year + "-" + monthPadded + " ('Security Updates' | 'Critical Updates' | 'Updates') & 'windows'"
		queryList = append(queryList, year+"-"+monthPadded)
		queryList = append(queryList, catalogSearchQuery)

		if kbIdList != nil && len(kbIdList) > 0 {
			prepareKbIdQuery := ""
			for i, kbId := range kbIdList {
				if len(prepareKbIdQuery)+len(kbId)+3 > 100 {
					prepareKbIdQuery = prepareKbIdQuery[:len(prepareKbIdQuery)-3]
					queryList = append(queryList, prepareKbIdQuery)
					prepareKbIdQuery = ""
				}
				if i == len(kbIdList)-1 {
					prepareKbIdQuery += kbId
					queryList = append(queryList, prepareKbIdQuery)
				} else {
					prepareKbIdQuery += kbId + " | "
				}
			}
		}

		NewUpdateCatalogService().ProcessUpdateCatalogDataByQueryList(queryList)
	}
}

func (service WindowsPatchService) GetAllWindowsPatch(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	queryCondition := ""
	qualifications := filter.Qualification
	if len(qualifications) == 0 || qualifications == nil {
		qualifications = []rest.Qualification{}
		qualifications = append(qualifications, rest.BuildQualification("publishState", "not_equals", "Expired", constant.AND.String()))
	} else {
		queryCondition += "\"" + common.ToSnakeCase("publishState") + "\" != 'Expired' AND "
	}
	queryCondition += "\"" + common.ToSnakeCase("title") + "\" NOT ILIKE '%LanguageFeatureOnDemand%' AND "
	queryCondition += "\"" + common.ToSnakeCase("title") + "\" NOT ILIKE '%LanguagePack%' AND "
	queryCondition += "\"" + common.ToSnakeCase("title") + "\" NOT ILIKE '%Language Pack%' AND "
	queryCondition += "\"" + common.ToSnakeCase("title") + "\" NOT ILIKE '%LanguageInterfacePack%' AND "
	queryCondition += "\"" + common.ToSnakeCase("title") + "\" NOT ILIKE '%Language Interface Pack%' "

	filter.Qualification = qualifications
	countQuery := rest.PrepareQueryFromSearchFilterWithCondition(filter, common.WindowsPatch.String(), true, queryCondition)
	var responsePage rest.ListResponseRest
	var packagePageList []windows2.WindowsPatch
	var err error
	count := service.Repository.Count(countQuery)
	if count > 0 {
		searchQuery := rest.PrepareQueryFromSearchFilterWithCondition(filter, common.WindowsPatch.String(), false, queryCondition)
		packagePageList, err = service.Repository.GetAllWindowsPatch(searchQuery)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = packagePageList
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service WindowsPatchService) GenerateCabAndMsuForAllPatches() error {
	filter := rest.SearchFilter{
		Qualification: []rest.Qualification{},
	}
	searchQuery := rest.PrepareQueryFromSearchFilter(filter, common.WindowsPatch.String(), false)
	patches, err := service.Repository.GetAllWindowsPatch(searchQuery)
	if err != nil {
		return err
	}
	if len(patches) > 0 {
		numWorkers := 32
		jobs := make(chan windows2.WindowsPatch)
		var wg sync.WaitGroup
		for index := 0; index < numWorkers; index++ {
			wg.Add(1)
			go func(workerID int) {
				defer wg.Done()
				for patch := range jobs { // Listen for patches
					var fileDataList []model.FileData
					var ignoreUpdate bool
					for _, detail := range patch.FileDetails {
						if detail.FetchFromMSU {
							ignoreUpdate = true
							break
						}
					}
					if !ignoreUpdate {
						downloadURLs := getDownloadUrlsByWindowsPatchUUID(patch.UUID)
						if downloadURLs != nil && len(downloadURLs) > 0 {
							for _, url := range downloadURLs {
								fileName := url[strings.LastIndex(url, "/")+1:]
								fileSize := common.GetFileSizeFromUrl(url)
								fileDataList = append(fileDataList, model.FileData{
									FileName:     fileName,
									DownloadUrl:  url,
									Size:         fileSize,
									FetchFromMSU: true,
								})
							}
						}

						if len(fileDataList) > 0 {
							var finalFileDetails []model.FileData
							for _, detail := range patch.FileDetails {
								isFileFound := false
								for _, data := range fileDataList {
									if data.DownloadUrl == detail.DownloadUrl {
										isFileFound = true
										break
									}
								}
								if !isFileFound {
									detail.FetchFromMSU = false
									finalFileDetails = append(finalFileDetails, detail)
								}
							}

							finalFileDetails = append(finalFileDetails, fileDataList...)

							if len(finalFileDetails) > 0 {
								logger.ServiceLogger.Info(fmt.Sprintf("Msu update Patches processing: %s", patch.UUID))
								patch.FileDetails = common.RemoveDuplicates(finalFileDetails) // Replace with the new combined list
								patch.UpdatedTime = time.Now().UnixMilli()
								_, err := service.Repository.Update(&patch)
								if err != nil {
									logger.ServiceLogger.Error(err)
								}
							}
						} else {
							if patch.FileDetails != nil && len(patch.FileDetails) > 0 {
								var finalFileDetails []model.FileData
								for _, detail := range patch.FileDetails {
									detail.FetchFromMSU = false
									finalFileDetails = append(finalFileDetails, detail)
								}

								if len(finalFileDetails) > 0 {
									logger.ServiceLogger.Info(fmt.Sprintf("Msu update Patches processing: %s", patch.UUID))
									patch.FileDetails = common.RemoveDuplicates(finalFileDetails) // Replace with the new combined list
									patch.UpdatedTime = time.Now().UnixMilli()
									_, err := service.Repository.Update(&patch)
									if err != nil {
										logger.ServiceLogger.Error(err)
									}
								}
							}
						}
					}
					err = service.submitTaskToFetchCab(patch)
					if err != nil {
						logger.ServiceLogger.Error("Patch : "+patch.UUID, " : "+patch.Name+" ", err)
					}
					logger.ServiceLogger.Info(fmt.Sprintf("Cab Patches completed: %s", patch.UUID))
					logger.ServiceLogger.Info(fmt.Sprintf("Worker-%d finished: %s", workerID, patch.UUID))
				}
			}(index + 1)
		}
		logger.ServiceLogger.Info(fmt.Sprintf("Cab and Upadate Catalog Total Patches : %d", len(patches)))
		for index, patch := range patches {
			if !patch.TempData {
				logger.ServiceLogger.Info(fmt.Sprintf("processing patches : %d, %s", index, patch.UUID))
				jobs <- patch
			}
		}
		close(jobs)
		wg.Wait()
		service.Create7ZCabFolder()
	}
	return nil
}
