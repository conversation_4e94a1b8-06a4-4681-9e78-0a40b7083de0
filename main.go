package main

import (
	"fmt"
	"github.com/joho/godotenv"
	"github.com/uptrace/bun"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/db"
	"patch-central-repo/handler"
	"patch-central-repo/logger"
	"patch-central-repo/server"
	"path/filepath"
)

func main() {
	channel := make(chan struct{})
	WorkingDir := common.CurrentWorkingDir()
	//Load app.config file
	err := godotenv.Load(filepath.Join(WorkingDir, "app.config"))
	if err != nil {
		fmt.Printf("Error loading app.config file %s", err.Error())
	}
	logDir := filepath.Join(WorkingDir, "logs")
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		err := os.Mkdir(logDir, os.ModePerm)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}
	logLevel := common.GetEnv("LOG_LEVEL", "debug")
	logMaxSize := common.GetEnvNumeric("LOG_MAX_SIZE", 10)
	logMaxAge := common.GetEnvNumeric("LOG_MAX_AGE", 30)
	logger.ConfigLogger(logDir, logLevel, logMaxSize, logMaxAge)

	logger.ServiceLogger.Info("starting central repository server...")
	logger.ServiceLogger.Info("initializing database connection...")
	_, err = db.Connect()
	if err != nil {
		panic(err)
	}
	logger.ServiceLogger.Info("database connection initialized successfully...")
	logger.ServiceLogger.Info("starting system on board processing...")
	handler.SystemOnBoardService()
	logger.ServiceLogger.Info("system on board processing completed...")
	tlsServer := server.NewServer()
	err = tlsServer.ListenAndServe()
	if err != nil {
		panic(err)
	}

	defer func(tlsServer *server.HttpServer) {
		err := tlsServer.Shutdown()
		if err != nil {
			logger.ServiceLogger.Error(err)

		}
	}(tlsServer)

	defer func(Connection *bun.DB) {
		err := Connection.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(db.Connection)

	<-channel
}
