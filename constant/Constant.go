package constant

const (
	DATE_FORMATE_MM_DD_YYYY = "1/2/2006"
)

var (
	AtLeastOneFileInstallationKBIds = []string{"4052623", "2267602"}
)

func Contains(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

type Condition int

const (
	AND Condition = iota + 1
	OR
)

func (c Condition) String() string {
	switch c {
	case AND:
		return "and"
	case OR:
		return "or"
	default:
		return ""
	}
}

func (c Condition) ToCondition(condition string) Condition {
	switch condition {
	case "and":
		return AND
	case "or":
		return OR
	default:
		return 0
	}
}
